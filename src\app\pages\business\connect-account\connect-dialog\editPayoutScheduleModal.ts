import { Component, OnInit, inject } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MessageService } from 'primeng/api';
import { DialogService, DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { PrimengModules } from '../../../../core/utils/primeng';
import { LoaderService } from '../../../../core/services/loader.service';
import { ServerApiService } from '../../../../core/services/server-api.service';
import { ApiService } from '../../../../core/services/api.service';
import { HttpParams } from '@angular/common/http';
import { createFormData, filterParams } from '../../../../core/utils/utils';
@Component({
    selector: 'app-edit-payout-schedule-modal',
    imports: [PrimengModules],
    template: `
                    <div class="w-full">
                        <form [formGroup]="editPayoutScheduleForm" (ngSubmit)="saveEditPayoutSchedule()">
                            <div class="grid grid-cols-1 md:grid-cols-1 gap-4">
                                <!-- Payout interval -->
                                 <div>
                                    <label for="schedule_interval" class="required-label block font-bold mb-3">Payout Interval</label>
                                    <p-select [options]="payout_intervals" optionLabel="name" optionValue="id" [showClear]="true"
                                        formControlName="schedule_interval" placeholder="Select a payout interval" [appendTo]="'body'" required
                                        fluid />
                                    <app-form-error [control]="editPayoutScheduleForm.controls['schedule_interval']" [controlName]="'Payout Interval'"
                                        [apiErrorType]="'schedule_interval'" />
                                </div>
                                <!-- Delay by days -->
                                 <div>
                                    <label for="schedule_delay_days" class="required-label block font-bold mb-3">Delay by days</label>
                                   <p-select [options]="delay_days" optionLabel="day" optionValue="day" [showClear]="true"
                                        formControlName="schedule_delay_days" placeholder="Select a delay days" [appendTo]="'body'" required
                                        fluid />
                                    <app-form-error [control]="editPayoutScheduleForm.controls['schedule_delay_days']" [controlName]="'Delay by days'"
                                        [apiErrorType]="'schedule_delay_days'" />
                                </div>
                                @if(editPayoutScheduleForm.get('schedule_interval')?.value === 'weekly'){   
                                    <!-- schedule_weekly_anchor -->
                                     <div>
                                        <label for="schedule_weekly_anchor" class="required-label block font-bold mb-3">On</label>
                                        <p-select [options]="weekly_anchors" optionLabel="name" optionValue="id" [showClear]="true"
                                            formControlName="schedule_weekly_anchor" placeholder="Select a weekly anchor" [appendTo]="'body'" required
                                            fluid />
                                        <app-form-error [control]="editPayoutScheduleForm.controls['schedule_weekly_anchor']" [controlName]="'Weekly Anchor'"
                                            [apiErrorType]="'schedule_weekly_anchor'" />
                                    </div>
                                }

                                @if(editPayoutScheduleForm.get('schedule_interval')?.value === 'monthly'){   
                                    <!-- schedule_monthly_anchor -->
                                     <div>
                                        <label for="schedule_monthly_anchor" class="required-label block font-bold mb-3">On</label>
                                        <p-select [options]="monthly_anchors" optionLabel="name" optionValue="id" [showClear]="true"
                                            formControlName="schedule_monthly_anchor" placeholder="Select a monthly anchor" [appendTo]="'body'" required
                                            fluid />
                                        <app-form-error [control]="editPayoutScheduleForm.controls['schedule_monthly_anchor']" [controlName]="'Monthly Anchor'"
                                            [apiErrorType]="'schedule_monthly_anchor'" />
                                    </div>
                                }

                            </div>
                            <!-- submit  -->
                            <button type="submit" style="display: none"></button>
                        </form>
                    </div>
                    <div class="flex justify-end gap-2 mt-6 border-t border-gray-200 pt-2">
                        <p-button label="Cancel" icon="pi pi-times" text (click)="close()" />
                                <p-button label="Save" icon="pi pi-check" (click)="saveEditPayoutSchedule()" />
                    </div>
            `,
    providers: [DialogService],
})
export class EditPayoutScheduleModal implements OnInit {
    private fb = inject(FormBuilder);
    private messageService = inject(MessageService);
    private ref = inject(DynamicDialogRef);
    private config = inject(DynamicDialogConfig);
    private loaderService = inject(LoaderService);
    private serverApiService = inject(ServerApiService);

    editPayoutScheduleForm!: FormGroup;
    connectAccounts: any = null;

    payout_intervals = [{ id: "daily", name: "Daily" }, { id: "weekly", name: "Weekly" }, { id: "monthly", name: "Monthly" }, { id: "manual", name: "Manual" }];
    delay_days = [{ day: 7 }, { day: 8 }, { day: 9 }, { day: 10 }, { day: 11 }, { day: 12 }, { day: 13 }, { day: 14 }, { day: 15 }, { day: 16 }, { day: 17 }, { day: 18 }, { day: 19 }, { day: 20 }, { day: 21 }, { day: 22 }, { day: 23 }, { day: 24 }, { day: 25 }, { day: 26 }, { day: 27 }, { day: 28 }, { day: 29 }, { day: 30 }]
    weekly_anchors = [{ id: "monday", name: "Monday" }, { id: "tuesday", name: "Tuesday" }, { id: "wednesday", name: "Wednesday" }, { id: "thursday", name: "Thursday" }, { id: "friday", name: "Friday" }, { id: "saturday", name: "Saturday" }, { id: "sunday", name: "Sunday" }]
    monthly_anchors = [{ id: 1, name: "1st" }, { id: 2, name: "2nd" }, { id: 3, name: "3rd" }, { id: 4, name: "4th" }, { id: 5, name: "5th" }, { id: 6, name: "6th" }, { id: 7, name: "7th" }, { id: 8, name: "8th" }, { id: 9, name: "9th" }, { id: 10, name: "10th" }, { id: 11, name: "11th" }, { id: 12, name: "12th" }, { id: 13, name: "13th" }, { id: 14, name: "14th" }, { id: 15, name: "15th" }, { id: 16, name: "16th" }, { id: 17, name: "17th" }, { id: 18, name: "18th" }, { id: 19, name: "19th" }, { id: 20, name: "20th" }, { id: 21, name: "21st" }, { id: 22, name: "22th" }, { id: 23, name: "23th" }, { id: 24, name: "24th" }, { id: 25, name: "25th" }, { id: 26, name: "26th" }, { id: 27, name: "27th" }, { id: 28, name: "28th" }, { id: 29, name: "29th" }, { id: 30, name: "30th" }];

    constructor() {
        this.connectAccounts = this.config.data.connectAccounts;
    }

    ngOnInit() {
        this.createEditPayoutScheduleForm();
    }

    createEditPayoutScheduleForm() {
        this.editPayoutScheduleForm = this.fb.group({
            id: [this.connectAccounts.id],
            s_account_id: [this.connectAccounts.s_account_id],
            schedule_delay_days: [this.connectAccounts?.stripe_connect_account?.settings?.payouts?.schedule?.delay_days, Validators.required],
            schedule_interval: [this.connectAccounts?.stripe_connect_account?.settings?.payouts?.schedule?.interval, Validators.required],
            schedule_weekly_anchor: [this.connectAccounts?.stripe_connect_account?.settings?.payouts?.schedule?.weekly_anchor],
            schedule_monthly_anchor: [this.connectAccounts?.stripe_connect_account?.settings?.payouts?.schedule?.monthly_anchor],
        });
    }

    saveEditPayoutSchedule() {
        if (!this.editPayoutScheduleForm.valid) {
            this.editPayoutScheduleForm.markAllAsTouched();
            this.editPayoutScheduleForm.updateValueAndValidity();
            this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Please fill all the required fields', life: 3000, });
            return;
        }
        const formData = createFormData(this.editPayoutScheduleForm.value);
        this.loaderService.show();
        this.serverApiService.post(`connect/connect-accounts/${this.connectAccounts.id}/payout-settings`, formData).subscribe({
            next: (res: any) => {
                this.ref.close(res);
                this.messageService.add({ severity: 'success', summary: 'Successful', detail: 'Payout Schedule Updated', life: 3000, });
            },
            error: (err: any) => {
                this.loaderService.hide();
                this.messageService.add({ severity: 'error', summary: 'Error', detail: err.error.message, life: 3000, });
            },
            complete: () => { this.loaderService.hide(); },
        });
    }

    close() {
        this.ref.close();
    }
}
