import { Component, Input, OnChanges, OnInit } from '@angular/core';
import { PrimengModules } from '../../../core/utils/primeng';

@Component({
  selector: 'app-business-fees-charges',
  imports: [PrimengModules],
  standalone: true,
  templateUrl: './business-fees-charges.component.html',
  styleUrl: './business-fees-charges.component.scss'
})
export class BusinessFeesChargesComponent implements OnInit, OnChanges {
  @Input() business: any = null;

  business_commissions: any = null;

  constructor() {
  }

  ngOnInit() { }

  ngOnChanges() {
    this.business_commissions = this.business?.business_commissions;
  }
}
