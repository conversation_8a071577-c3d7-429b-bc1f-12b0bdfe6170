import { Component, OnInit, inject } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ApiService } from '../../../../core/services/api.service';
import { MessageService } from 'primeng/api';
import { DialogService, DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { PrimengModules } from '../../../../core/utils/primeng';
import { LoaderService } from '../../../../core/services/loader.service';
import { ServerApiService } from '../../../../core/services/server-api.service';
import { createFormData } from '../../../../core/utils/utils';

@Component({
    selector: 'app-add-bank-account-modal',
    imports: [PrimengModules],
    template: `
                <div class="w-full">
                    <form [formGroup]="addBankAccountForm">
                        <div class="grid grid-cols-1 md:grid-cols-1 gap-4">
                            <!-- account_holder_name -->
                             <div>
                                <label for="account_holder_name" class="required-label block font-bold mb-3">Account Holder Name</label>
                                <input type="text" aria-label="Account Holder Name" pInputText id="account_holder_name"
                                    formControlName="account_holder_name" placeholder="Account Holder Name" required autofocus fluid />
                                <app-form-error [control]="addBankAccountForm.controls['account_holder_name']" [controlName]="'Account Holder Name'"
                                    [apiErrorType]="'account_holder_name'" />
                            </div>
                            @if(!bankAccount?.id){
                            <!-- account_number -->
                             <div>
                                <label for="account_number" class="required-label block font-bold mb-3">Account Number</label>
                                <input type="text" aria-label="Account Number" pInputText id="account_number"
                                    formControlName="account_number" placeholder="Account Number" required autofocus fluid />
                                <app-form-error [control]="addBankAccountForm.controls['account_number']" [controlName]="'Account Number'"
                                    [apiErrorType]="'account_number'" />
                            </div>
                            <!-- sort_code -->
                             <div>
                                <label for="sort_code" class="required-label block font-bold mb-3">Sort Code</label>
                                <input type="text" aria-label="Sort Code" pInputText id="sort_code"
                                    formControlName="sort_code" placeholder="Sort Code" required autofocus fluid />
                                <app-form-error [control]="addBankAccountForm.controls['sort_code']" [controlName]="'Sort Code'"
                                    [apiErrorType]="'sort_code'" />
                            </div>
                            }
                        </div>
                    </form>
                </div>
                <div class="flex justify-end gap-2 mt-6 border-t border-gray-200 pt-2">
                    <p-button label="Cancel" icon="pi pi-times" text (click)="close()" />
                    <p-button label="Save" icon="pi pi-check" (click)="saveAddBankAccount()" />
                </div>
            `,
    providers: [DialogService],
})
export class AddBankAccountModal implements OnInit {
    private fb = inject(FormBuilder);
    private messageService = inject(MessageService);
    private ref = inject(DynamicDialogRef);
    private config = inject(DynamicDialogConfig);
    private loaderService = inject(LoaderService);
    private serverApiService = inject(ServerApiService);

    addBankAccountForm!: FormGroup;
    connectAccounts_id: any = null;
    bankAccount: any = null;
    constructor() {
        this.createAddBankAccountForm();
    }
    ngOnInit() {
        this.connectAccounts_id = this.config.data.connectAccounts_id;
        this.bankAccount = this.config.data.bankAccount;
        if (this.bankAccount) {
            this.addBankAccountForm.patchValue(this.bankAccount);
            this.addBankAccountForm.removeControl('account_number');
            this.addBankAccountForm.removeControl('sort_code');
        }
    }

    createAddBankAccountForm() {
        this.addBankAccountForm = this.fb.group({
            id: [''],
            account_holder_name: ['', Validators.required],
            account_number: ['', Validators.required],
            sort_code: ['', Validators.required],
        });
    }

    saveAddBankAccount() {
        if (!this.addBankAccountForm.valid) {
            this.addBankAccountForm.markAllAsTouched();
            this.addBankAccountForm.updateValueAndValidity();
            this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Please fill all the required fields', life: 3000, });
            return;
        }
        const formData = createFormData(this.addBankAccountForm.value);
        this.loaderService.show();
        const url = this.bankAccount ? `connect/connect-accounts/${this.connectAccounts_id}/bank-accounts/${this.bankAccount.id}` : `connect/connect-accounts/${this.connectAccounts_id}/bank-accounts`;
        this.serverApiService.post(url, formData).subscribe({
            next: (res: any) => { this.ref.close(res); },
            error: (err: any) => {
                this.loaderService.hide();
                this.messageService.add({ severity: 'error', summary: 'Error', detail: err.error.message, life: 3000, });
            },
            complete: () => { this.loaderService.hide(); },
        });
    }

    close() {
        this.ref.close();
    }
}
