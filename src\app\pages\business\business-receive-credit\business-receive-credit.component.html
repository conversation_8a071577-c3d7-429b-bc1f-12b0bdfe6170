<p-table #dt [value]="receiveCredits" [rows]="10" [paginator]="true" [resetPageOnSort]="false" [lazy]="true"
    (onLazyLoad)="onLazyLoad($event)" [rowHover]="true" dataKey="id" [totalRecords]="totalRecords"
    [tableStyle]="{ 'min-width': '100rem' }" [scrollable]="true" scrollHeight="80vh"
    currentPageReportTemplate="Showing {first} to {last} of {totalRecords} receive credits"
    [showCurrentPageReport]="true" [rowsPerPageOptions]="[10, 20, 30]">
    <ng-template #caption>
        <p-toolbar [style]="{'border': 'none','padding': '0'}">
            <ng-template #start>
                <p class="font-medium font-bold text-2xl">Receive Credits</p>
            </ng-template>

            <ng-template #end>
                <p-iconfield class="mr-2">
                    <p-inputicon styleClass="pi pi-search" />
                    <input pInputText type="text" [formControl]="search" placeholder="Search..." />
                </p-iconfield>
                <p-datepicker [formControl]="date_range" selectionMode="range" [readonlyInput]="true" class="mr-2"
                    [showClear]="true" placeholder="Select a date range" pTooltip="Filter by date range"
                    tooltipPosition="top" dateFormat="dd-mm-yy" [appendTo]="'body'" />
                <p-button label="Hard Refresh" icon="pi pi-refresh" severity="help" class="mr-2"
                    (click)="fetchReceiveCreditsHardRefresh()" />
                <p-button label="Export" icon="pi pi-download" class="mr-2" (click)="exportToExcel()" />
            </ng-template>
        </p-toolbar>

    </ng-template>
    <ng-template #header>
        <tr>
            <!-- account_holder_name -->
            <th pSortableColumn="account_holder_name">
                Account Holder Name
                <p-sortIcon field="account_holder_name" />
            </th>
            <!-- financial_address.account_holder_name -->
            <th pSortableColumn="financial_address.account_holder_name">
                Financial Holder Name
                <p-sortIcon field="financial_address.account_holder_name" />
            </th>
            <!-- last4 -->
            <th pSortableColumn="last4">
                Account Number
                <p-sortIcon field="last4" />
            </th>
            <!-- amount -->
            <th pSortableColumn="amount">
                Amount
                <p-sortIcon field="amount" />
            </th>
            <!-- currency -->
            <th pSortableColumn="currency">
                Currency
                <p-sortIcon field="currency" />
            </th>
            <!-- created -->
            <th pSortableColumn="created">
                Created
                <p-sortIcon field="created" />
            </th>
            <!-- status -->
            <th>
                Status
            </th>
            <th>Action</th>

        </tr>
    </ng-template>
    <ng-template #body let-receiveCredit>
        <tr>
            <td>{{ receiveCredit?.account_holder_name }}</td>
            <td>{{ receiveCredit?.financial_address?.account_holder_name }}</td>
            <td>XXXX{{ receiveCredit?.last4 }}</td>
            <td [ngClass]="{'text-green-500': receiveCredit?.amount > 0, 'text-red-500': receiveCredit?.amount < 0}">{{
                (receiveCredit?.amount/100) | currency:'GBP':'symbol':'1.2-2' }}</td>
            <td>{{ receiveCredit?.currency | uppercase }}</td>
            <td>{{ receiveCredit?.created | date:'dd-MM-yyyy HH:mm' }}</td>
            <td>
                <p-tag [severity]="getSeverity(receiveCredit?.status)"
                    value="{{ receiveCredit?.status | titlecase }}" />
            </td>
            <td>
                <p-button icon="pi pi-eye" class="mr-2" [rounded]="true" [outlined]="true" />
            </td>
        </tr>

    </ng-template>
    <ng-template #emptymessage>
        <tr>
            <td colspan="9" style="text-align: center !important;">No records found</td>
        </tr>
    </ng-template>
</p-table>

<p-confirmdialog [style]="{ width: '450px' }" />