import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { AuthService } from '../services/authentication.service';
import { MessageService } from 'primeng/api';

export const permissionGuard: CanActivateFn = (route, state): boolean => {
  const authService = inject(AuthService);
  const router = inject(Router);
  const messageService = inject(MessageService);
  const permissionPath = route.data['permission'];
  const permissions = authService._permissions;
  const superman = authService._superman;
  console.log(`superman: `, superman);
  if (superman && authService.isAdmin()) return true;

  const hasPermission = getNestedPermission(permissions, permissionPath);
  if (hasPermission && authService.isAdmin()) {
    return true;
  }

  messageService.add({
    severity: 'error',
    summary: 'Error',
    detail: 'You do not have permission to access this page',
    life: 3000,
  });

  router.navigate(['/home']);
  return false;
};


function getNestedPermission(obj: any, path: string): boolean {
  if (!obj || !path) return false;
  return path.split('.').reduce((acc, key) => (acc && acc[key] !== undefined ? acc[key] : undefined), obj) === true;
}