import { Routes } from '@angular/router';
import { AppLayout } from './layout/component/app.layout';
import { DashboardComponent } from './pages/dashboard/dashboard.component';
import { authGuard } from './core/guards/auth.guard';
import { notAuthGuard } from './core/guards/not-auth.guard';
import { Notfound } from './pages/notfound/notfound';
import { UnderMaintenanceComponent } from './pages/under-maintenance/under-maintenance.component';
import { AdminLoginComponent } from './pages/auth/admin-login/admin-login.component';
export const routes: Routes = [
  { path: '', redirectTo: '/auth/login', pathMatch: 'full' },
  {
    path: '',
    canActivate: [authGuard],
    component: AppLayout,
    children: [
      { path: '', loadChildren: () => import('./pages/pages.routes') },
    ],
  },
  {
    path: 'auth',
    canActivate: [notAuthGuard],
    loadChildren: () => import('./pages/auth/auth.routes'),
  },
  {
    path: 'admin/login',
    component: AdminLoginComponent,
    canActivate: [notAuthGuard],
  },
  { path: 'under-maintenance', component: UnderMaintenanceComponent },
  {
    path: '**',
    redirectTo: 'notfound',
  },
  { path: 'notfound', component: Notfound },
];
