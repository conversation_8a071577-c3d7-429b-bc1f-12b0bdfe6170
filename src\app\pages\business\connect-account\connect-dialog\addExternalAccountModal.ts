import { Component, OnInit, inject } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { MessageService } from 'primeng/api';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';
import { DialogService } from 'primeng/dynamicdialog';
import { LoaderService } from '../../../../core/services/loader.service';
import { ServerApiService } from '../../../../core/services/server-api.service';
import { PrimengModules } from '../../../../core/utils/primeng';
import { createFormData } from '../../../../core/utils/utils';

@Component({
    selector: 'app-add-external-account-modal',
    imports: [PrimengModules],
    template: `
                <div class="w-full">
                    <form [formGroup]="addExternalAccountForm">
                        <div class="grid grid-cols-1 md:grid-cols-1 gap-4">
                            <!-- account_holder_name -->
                             <div>
                                <label for="account_holder_name" class="required-label block font-bold mb-3">Account Holder Name</label>
                                <input type="text" aria-label="Account Holder Name" pInputText id="account_holder_name"
                                    formControlName="account_holder_name" placeholder="Account Holder Name" required autofocus fluid />
                                <app-form-error [control]="addExternalAccountForm.controls['account_holder_name']" [controlName]="'Account Holder Name'"
                                    [apiErrorType]="'account_holder_name'" />
                            </div>
                             <!-- account_number -->
                              <div>
                                <label for="account_number" class="required-label block font-bold mb-3">Account Number</label>
                                <input type="text" aria-label="Account Number" pInputText id="account_number"
                                    formControlName="account_number" placeholder="Account Number" required autofocus fluid />
                                <app-form-error [control]="addExternalAccountForm.controls['account_number']" [controlName]="'Account Number'"
                                    [apiErrorType]="'account_number'" />
                            </div>
                              <!-- routing_number -->
                               <div>
                                <label for="routing_number" class="required-label block font-bold mb-3">Sort Code</label>
                                <input type="text" aria-label="Sort Code" pInputText id="routing_number"
                                    formControlName="routing_number" placeholder="Sort Code" required autofocus fluid />
                                <app-form-error [control]="addExternalAccountForm.controls['routing_number']" [controlName]="'Sort Code'"
                                    [apiErrorType]="'routing_number'" />
                            </div>
                               <!-- description -->
                                <div>
                                <label for="description" class="required-label block font-bold mb-3">Description</label>
                                <input type="text" aria-label="Description" pInputText id="description"
                                    formControlName="description" placeholder="Description" autofocus fluid />
                                <app-form-error [control]="addExternalAccountForm.controls['description']" [controlName]="'Description'"
                                    [apiErrorType]="'description'" />
                            </div>
                                <!-- statement_descriptor -->
                                 <div>
                                <label for="statement_descriptor" class="block font-bold mb-3">Statement Descriptor</label>
                                <input type="text" aria-label="Statement Descriptor" pInputText id="statement_descriptor"
                                    formControlName="statement_descriptor" placeholder="Statement Descriptor" autofocus fluid />
                                <app-form-error [control]="addExternalAccountForm.controls['statement_descriptor']" [controlName]="'Statement Descriptor'"
                                    [apiErrorType]="'statement_descriptor'" />
                            </div>
                                 <!-- account_holder_type -->
                                  <div>
                                <label for="account_holder_type" class="required-label block font-bold mb-3">Account Holder Type</label>
                                <p-select [options]="account_holder_types" optionLabel="name" optionValue="id" [showClear]="true"
                                    formControlName="account_holder_type" placeholder="Select a account holder type" [appendTo]="'body'" required fluid />      
                                <app-form-error [control]="addExternalAccountForm.controls['account_holder_type']" [controlName]="'Account Holder Type'"    
                                    [apiErrorType]="'account_holder_type'" />
                            </div>
                                  <!-- country -->
                                    <div>
                                <label for="country" class="required-label block font-bold mb-3">Country</label>
                                <p-select [options]="countries" optionLabel="name" optionValue="iso" [showClear]="true"
                                    formControlName="country" placeholder="Select a country" [appendTo]="'body'" required fluid />
                                <app-form-error [control]="addExternalAccountForm.controls['country']" [controlName]="'Country'"
                                    [apiErrorType]="'country'" />
                            </div>
                        </div>
                    </form>
                </div>
                <div class="flex justify-end gap-2 mt-6 border-t border-gray-200 pt-2">
                    <p-button label="Cancel" icon="pi pi-times" text (click)="close()" />
                    <p-button label="Save" icon="pi pi-check" (click)="saveAddExternalAccount()" />
                </div>
            `,
    providers: [DialogService],
})
export class AddExternalAccountModal implements OnInit {
    private fb = inject(FormBuilder);
    private messageService = inject(MessageService);
    private ref = inject(DynamicDialogRef);
    private config = inject(DynamicDialogConfig);
    private loaderService = inject(LoaderService);
    private serverApiService = inject(ServerApiService);

    addExternalAccountForm!: FormGroup;
    account_holder_types = [{ id: "individual", name: "Individual" }, { id: "company", name: "Company" }];
    countries: any[] = [];
    business: any = null;
    connectAccounts_id: any = null;

    constructor() {
        this.createAddExternalAccountForm();
    }

    ngOnInit() {
        this.countries = this.config.data.countries;
        this.business = this.config.data.business;
        this.connectAccounts_id = this.config.data.connectAccounts_id;
        if (!this.config.data.isEdit) {
            this.addExternalAccountForm.patchValue({
                country: this.config.data.business.country.iso,
                currency: this.config.data.business.country.currency_code,
                business_id: this.business.id,
                connect_account_id: this.connectAccounts_id,
            });
        } else {
            this.addExternalAccountForm.patchValue(this.config.data.externalAccount);
        }
    }

    createAddExternalAccountForm() {
        this.addExternalAccountForm = this.fb.group({
            account_number: ['', Validators.required],
            routing_number: ['', Validators.required],
            country: ['', Validators.required],
            currency: [''],
            account_holder_name: ['', Validators.required],
            account_holder_type: ['individual', Validators.required],
            s_bank_account_id: [''],
            s_account_id: [''],
            connect_account_id: [''],
            business_id: [''],
            description: ['Ubsidi', Validators.required],
            statement_descriptor: [''],
            default: [false],
            is_customer: [true],
        });
    }

    saveAddExternalAccount() {
        if (!this.addExternalAccountForm.valid) {
            this.addExternalAccountForm.markAllAsTouched();
            this.addExternalAccountForm.updateValueAndValidity();
            this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Please fill all the required fields', life: 3000, });
            return;
        }
        const formData = createFormData(this.addExternalAccountForm.value);
        this.loaderService.show();
        const url = this.config.data.isEdit ? `connect/connect-accounts/${this.connectAccounts_id}/external-accounts/${this.config.data.externalAccount.id}` : `connect/connect-accounts/${this.connectAccounts_id}/external-accounts`;
        this.serverApiService.post(url, formData).subscribe({
            next: (res: any) => { this.ref.close(res); },
            error: (err: any) => {
                this.loaderService.hide();
                this.messageService.add({ severity: 'error', summary: 'Error', detail: err.error.message, life: 3000, });
            },
            complete: () => { this.loaderService.hide(); },
        });
    }

    close() {
        this.ref.close();
    }
}
