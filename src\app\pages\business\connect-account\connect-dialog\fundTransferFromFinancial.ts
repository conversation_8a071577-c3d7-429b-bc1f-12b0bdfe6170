import { Component, OnInit, inject } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MessageService } from 'primeng/api';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { DialogService } from 'primeng/dynamicdialog';
import { PrimengModules } from '../../../../core/utils/primeng';
import { LoaderService } from '../../../../core/services/loader.service';
import { ServerApiService } from '../../../../core/services/server-api.service';
import { ApiService } from '../../../../core/services/api.service';

@Component({
    selector: 'app-fund-transfer-modal',
    imports: [PrimengModules],
    template: `
     
<div class="w-full">
    <form [formGroup]="fundTransferForm">
        <div class="grid grid-cols-1 md:grid-cols-1 gap-4">
            <!-- bank_account_id -->
            <div>
                <label for="bank_account_id" class="required-label block font-bold mb-3">Bank Account</label>
                <p-select [options]="bank_accounts" optionValue="id" [showClear]="true"
                    formControlName="bank_account_id" placeholder="Select a bank account" [appendTo]="'body'" required
                    fluid>
                    <ng-template #selectedItem let-selectedOption>
                        <div class="flex items-center gap-2">
                            <div>{{ selectedOption.account_holder_name }} - XXXX{{selectedOption.last4}}</div>
                        </div>
                    </ng-template>
                    <ng-template let-option pTemplate="item">
                      {{option.account_holder_name}} - XXXX{{option.last4}}
                    </ng-template>
                </p-select>
                <app-form-error [control]="fundTransferForm.controls['bank_account_id']" [controlName]="'Bank Account'"
                    [apiErrorType]="'bank_account_id'" />
            </div>

            <!-- amount -->
            <div>
                <label for="amount" class="required-label block font-bold mb-3">Amount</label>
                <p-inputnumber formControlName="amount" aria-label="Amount" inputId="amount" 
                    placeholder="Amount"  mode="decimal" [minFractionDigits]="2" required fluid />
                <app-form-error [control]="fundTransferForm.controls['amount']" [controlName]="'Amount'"
                    [apiErrorType]="'amount'" />
            </div>
            <!-- description -->
            <div>
                <label for="description" class="required-label block font-bold mb-3">Description</label>
                <input type="text" aria-label="Description" pInputText id="description" formControlName="description"
                    placeholder="Description" required autofocus fluid />
                <app-form-error [control]="fundTransferForm.controls['description']" [controlName]="'Description'"
                    [apiErrorType]="'description'" />
            </div>
        </div>
    </form>
</div>
<div class="flex justify-end gap-2 mt-6 border-t border-gray-200 pt-2">
    <p-button label="Cancel" icon="pi pi-times" text (click)="close()" />
    <p-button label="Save" icon="pi pi-check" (click)="saveFundTransfer()" />
</div>
      `,
    providers: [DialogService],
})
export class FundTransferFromFinancialModal implements OnInit {
    private fb = inject(FormBuilder);
    private messageService = inject(MessageService);
    private ref = inject(DynamicDialogRef);
    private config = inject(DynamicDialogConfig);
    private loaderService = inject(LoaderService);
    private serverApiService = inject(ServerApiService);

    connectAccounts_id: any = null;
    financialAccount_id: any = null;
    bank_accounts: any[] = [];
    fundTransferForm!: FormGroup;
    ngOnInit() {
        this.connectAccounts_id = this.config.data.connectAccounts_id;
        this.financialAccount_id = this.config.data.financialAccount_id;
        this.bank_accounts = this.config.data.bank_accounts;
        this.createFundTransferForm();
    }

    createFundTransferForm() {
        this.fundTransferForm = this.fb.group({
            bank_account_id: ['', Validators.required],
            amount: [null, Validators.required],
            description: ['', Validators.required],
        });
    }

    saveFundTransfer() {
        if (!this.fundTransferForm.valid) {
            this.fundTransferForm.markAllAsTouched();
            this.fundTransferForm.updateValueAndValidity();
            this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Please fill all the required fields', life: 3000, });
            return;
        }
        this.loaderService.show();
        this.serverApiService.post(`connect/connect-accounts/${this.connectAccounts_id}/financial-accounts/${this.financialAccount_id}/transfer`, this.fundTransferForm.value).subscribe({
            next: (res: any) => { 
                this.ref.close(res); 
                this.messageService.add({ severity: 'success', summary: 'Successful', detail: res.message, life: 3000, });
            },
            error: (err: any) => {
                this.loaderService.hide();
                this.messageService.add({ severity: 'error', summary: 'Error', detail: err.error.message, life: 3000, });
            },
            complete: () => { this.loaderService.hide(); },
        });
    }

    close() {
        this.ref.close();
    }
}
