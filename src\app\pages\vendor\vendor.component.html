<p-table #dt [value]="vendors" [rows]="10" [paginator]="true" [resetPageOnSort]="false"
    [globalFilterFields]="['first_name', 'contact_email']" [lazy]="true" (onLazyLoad)="onLazyLoad($event)"
    [rowHover]="true" dataKey="id" [totalRecords]="totalRecords" [tableStyle]="{ 'min-width': '60rem' }"
    [scrollable]="true" scrollHeight="80vh"
    currentPageReportTemplate="Showing {first} to {last} of {totalRecords} vendors" [showCurrentPageReport]="true"
    [rowsPerPageOptions]="[10, 20, 30]">
    <ng-template #caption>
        <p-toolbar [style]="{'border': 'none','padding': '0'}">
            <ng-template #start>
                <p class="font-medium font-bold text-2xl">Vendors</p>
            </ng-template>

            <ng-template #end>
                <p-iconfield class="mr-2">
                    <p-inputicon styleClass="pi pi-search" />
                    <input pInputText type="text" [formControl]="search" placeholder="Search..." />
                </p-iconfield>
                @if( authService._superman || authService._permissions?.vendors?.actions?.add) {
                <p-button label="New" icon="pi pi-plus" severity="secondary" class="mr-2"
                    (click)="openDialogCreateVendor()" />
                }
                <!-- <p-button label="Export" icon="pi pi-download" severity="secondary" class="mr-2"  /> -->
            </ng-template>
        </p-toolbar>

    </ng-template>
    <ng-template #header>
        <tr>
            <th pSortableColumn="first_name" pFrozenColumn>
                Name
                <p-sortIcon field="first_name" />
            </th>
            <th pSortableColumn="contact_emails">
                Email
                <p-sortIcon field="contact_emails" />
            </th>
            <th>
                Status
            </th>
            <th>Actions</th>
        </tr>
    </ng-template>
    <ng-template #body let-vendor>
        <tr>
            <td pFrozenColumn>{{ vendor.first_name }} {{ vendor.last_name }}</td>
            <td>{{ vendor.contact_emails }}</td>
            <td>
                <p-toggleswitch [ngModel]=" vendor.disabled  ? true : false"
                    (onChange)="toggleStatus($event, vendor)" />
            </td>
            <td>
                <!-- <p-button icon="pi pi-eye" class="mr-2" [rounded]="true" [outlined]="true"
                    (click)="viewVendor(vendor)" /> -->
                @if( authService._superman || authService._permissions?.vendors?.actions?.edit) {
                <p-button icon="pi pi-pencil" class="mr-2" [rounded]="true" [outlined]="true"
                    (click)="openDialogEditVendor(vendor)" />
                }
                @if( authService._superman || authService._permissions?.vendors?.actions?.delete) {
                <p-button icon="pi pi-trash" severity="danger" [rounded]="true" [outlined]="true"
                    (click)="deleteVendor(vendor)" />
                }
            </td>
        </tr>

    </ng-template>
    <ng-template #emptymessage>
        <tr>
            <td colspan="5" style="text-align: center !important;">No records found</td>
        </tr>
    </ng-template>
</p-table>

@if(vendorDialog){
<app-create-vendor [(vendorDialog)]="vendorDialog" (save)="handleSave($event)" [vendorData]="selectedVendor"
    [isEdit]="isEdit"></app-create-vendor>
}
<p-confirmdialog [style]="{ width: '450px' }" />