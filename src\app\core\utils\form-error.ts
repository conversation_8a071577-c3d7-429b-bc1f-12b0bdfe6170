import { Component, Input } from '@angular/core';
import { ErrorMessage, ErrorMessageService } from '../services/error-message.service';
import { PrimengModules } from './primeng';

const ERROR_OBJECTS: Record<string, string> = {
  required: '{{field}} is required',
  email: 'Please enter a valid email',
  notEquivalent: 'New password and confirm password does not match.',
  min: 'Please enter value more than {{value}}',
  max: 'Please enter value less than {{value}}',
  minlength: 'Please enter value with min length {{value}}',
  maxlength: 'Please enter value with max length {{value}}',
  pattern: 'Please enter valid value',
  totalHundred: 'The sum of Advance, Withhold, PR Fee must be equals to 100%',
  totalPrice: 'The sum of the split amounts must be equal to the price.',
  // website: 'Please enter a valid website URL',
  website: 'Website must be a valid URL including protocol identifier (i.e., http:// or https://)',
  invalidUrl: 'Please enter a valid URL',
  ccNumber: 'Please enter valid card number',
  not<PERSON>ero: 'Please enter value greater than 0',
  dateDiff: 'Please select correct date range',
  isValidDate: 'Please select correct date',
  validPassword: 'Password must contain 8 characters min, 1 upper, 1 number and 1 special character',
  zipCode: 'Please enter a valid 5-digit zip code',
  taxIdError: 'Tax ID must be a numeric value',
  onlyNumber: '{{value}} must be a numeric value',
  phoneError: 'Please enter a valid 10-digit phone number',
  correctDate: 'Please select correct date',
  ageInvalid: 'You must be at least 18 years old.',
  ageMismatch: 'Age does not match with date of birth.',
  maxSelection: 'You can select a maximum of 5 options.'
};

@Component({
  selector: 'app-form-error',
  standalone: true,
  imports: [PrimengModules],
  template: `
   <div [ngClass]="{'p-invalid': hasError()}" class="w-full">
  <ng-content></ng-content>

  <!-- Validation errors -->
  <ng-container *ngFor="let errorKey of errorKeys">
    <p-message
      *ngIf="control.hasError(errorKey) && control.touched"
        variant="simple" size="small" severity="error"
      [text]="formateError(errorObject[errorKey], control.errors[errorKey])">
    </p-message>
  </ng-container>

  <!-- API error -->
  <p-message
    *ngIf="apiErrorMessage"
    variant="simple" size="small" severity="error"
  >
    <div [innerHTML]="apiErrorMessage"></div>
  </p-message>
</div>

  `,
})
export class FormError {
  @Input() public control: any;
  @Input() public controlName: string = '';
  @Input() public apiErrorType?: string;
  @Input() public apiServiceUrl?: string;
  @Input() errorMessages: { [key: string]: string } = {};

  public errorObject: Record<string, string> = ERROR_OBJECTS;
  public errorKeys: string[] = [];
  public apiErrorMessage: string = '';

  constructor(private errorMessageService: ErrorMessageService) {
    this.errorMessageService.errors$.subscribe(
      (errors: ErrorMessage[]) => {
        if (errors?.length) {
          errors
            .filter(e => e.type === this.apiErrorType)
            .forEach(e => {
              this.apiErrorMessage = e.error;
            });
        } else {
          this.apiErrorMessage = '';
        }
      }
    );
  }

  ngOnInit() { }

  ngOnChanges() {
    this.errorObject = {
      ...ERROR_OBJECTS,
      ...this.errorMessages
    };
    this.errorKeys = Object.keys(this.errorObject);
  }

  ngAfterViewInit() {
    this.control.valueChanges.subscribe(() => {
      this.apiErrorMessage = '';
    });
  }

  formateError(errorMessage: string, errorObj: any): string {
    const types = ['min', 'max', 'requiredLength', 'onlyNumber', 'maxSelection'];
    types.forEach(type => {
      if (errorObj[type] !== undefined) {
        errorMessage = errorMessage.replace(/{{value}}/g, errorObj[type]);
      }
    });
    return errorMessage.replace(/{{field}}/g, this.controlName);
  }

  hasError(): boolean {
    return (
      (this.control.errors && this.control.touched) || !!this.apiErrorMessage
    );
  }
}
