import { HttpErrorResponse, HttpInterceptorFn, HttpResponse } from '@angular/common/http';
import { inject, PLATFORM_ID } from '@angular/core';
import { catchError, throwError, tap } from 'rxjs';
import { environment } from '../../../environments/environment';
import { Router } from '@angular/router';
import { ErrorMessageService } from '../services/error-message.service';
// import * as CryptoJS from 'crypto-js';

export const apiPrefixInterceptor: HttpInterceptorFn = (req, next) => {
  const router = inject(Router);
  const errorMessageService = inject(ErrorMessageService);
  let headers = req.headers;
  const accessToken = localStorage.getItem(environment.accessTokenKey);

  if (accessToken) {
    headers = headers.set('Authorization', `${accessToken}`);
  }

  const clonedReq = req.clone({ headers: headers });
  return next(clonedReq).pipe(tap(event => {
    if (event instanceof HttpResponse) {
      if (event.status === 200) {
        if (event.headers.has('accessToken')) {
          localStorage.setItem(environment.accessTokenKey, event.headers.get('accessToken') ?? '');
        }
      }
    }
  }),
    catchError((error: HttpErrorResponse) => {
      if (error.status === 401 || error.status === 403) {
        console.error('Unauthorized. Redirect to login maybe.');
        localStorage.removeItem(environment.accessTokenKey);
        window.location.reload();
      } if (error.status === 400 || error.status === 404 || error.status === 401) {
        const errorResponse: any = error;
        if (errorResponse.error) {
          if (errorResponse.error.validation) {
            errorMessageService.clear();
            Object.keys(errorResponse.error.validation.keys).forEach((key: string) => {
              errorMessageService.set(
                errorResponse.error.message,
                errorResponse.error.validation.keys[key],
                errorResponse.url
              );
            });
          } else { }
        }
      } else if (error.status === 500) {
        console.error('Server error. Please try again later.');
        // window.location.href = '/under-maintenance';
      } else if (error.status === 503) {
        console.error('Service Unavailable. Please try again later.');
        // window.location.href = '/under-maintenance';
      } else if (error.status === 0) {
        console.error('Network error. Please check your internet connection.');
        router.navigate(['/under-maintenance']);
      } else {
        console.error('API Error:', error.message);
      }

      return throwError(() => error);
    })
  );

};

// function notifierReverse(response: any) {
//   try {
//     var collaborator = CryptoJS.enc.Utf8.parse('Kp+O[BZFh;31#&hvw.nj,Z!j0{h6!QFn');
//     var leaker = CryptoJS.enc.Utf8.parse('v@XI!kaW3BK,@8ki');
//     var leakerData = CryptoJS.AES.decrypt(response, collaborator, {
//       keySize: 256 / 8,
//       iv: leaker,
//       mode: CryptoJS.mode.CBC,
//       padding: CryptoJS.pad.Pkcs7
//     });
//     var leakerText = leakerData.toString(CryptoJS.enc.Utf8);
//     return JSON.parse(leakerText);
//   } catch (e) {
//     var collaborator = CryptoJS.enc.Utf8.parse('Kp+O[BZFh;31#&hvw.nj,Z!j0{h6!QFn');
//     var leaker = CryptoJS.enc.Utf8.parse('v@XI!kaW3BK,@8ki');
//     var leakerData = CryptoJS.AES.decrypt(response, collaborator, {
//       keySize: 256 / 8,
//       iv: leaker,
//       mode: CryptoJS.mode.CBC,
//       padding: CryptoJS.pad.Pkcs7
//     });
//     var leakerText = leakerData.toString(CryptoJS.enc.Utf8);
//     return JSON.parse(leakerText);
//   }
// }
