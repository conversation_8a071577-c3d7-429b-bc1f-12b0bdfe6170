import {
  Component,
  EventEmitter,
  inject,
  Input,
  OnChanges,
  OnInit,
  Output,
} from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ApiService } from '../../../core/services/api.service';
import { LoaderService } from '../../../core/services/loader.service';
import { createFormData } from '../../../core/utils/utils';
import { MessageService } from 'primeng/api';
import { PrimengModules } from '../../../core/utils/primeng';
import { HttpParams } from '@angular/common/http';

@Component({
  selector: 'app-create-vendor',
  imports: [PrimengModules],
  standalone: true,
  templateUrl: './create-vendor.component.html',
  styleUrl: './create-vendor.component.scss',

})
export class CreateVendorComponent {
  private apiService = inject(ApiService);
  private loaderService = inject(LoaderService);
  private fb = inject(FormBuilder);
  private messageService = inject(MessageService);

  @Input() vendorDialog: boolean = false;
  @Output() vendorDialogChange = new EventEmitter<boolean>();
  @Output() save = new EventEmitter<any>();
  @Input() vendorData: any = null;
  @Input() isEdit: boolean = false;

  vendorForm!: FormGroup;
  countries: any[] = [];
  emails: any[] = [];
  numbers: any[] = [];
  constructor() {
    this.createVendorForm();
    this.fetchCountries();
  }

  fetchCountries() {
    this.loaderService.show();
    this.apiService.get('countries/mini').subscribe({
      next: (res: any) => {
        this.countries = res;
      },
      error: (err: any) => {
        this.loaderService.hide();
      },
      complete: () => {
        this.loaderService.hide();
      },
    });
  }
  createVendorForm() {
    this.vendorForm = this.fb.group({
      id: [''],
      first_name: ['', Validators.required],
      last_name: [''],
      contact_emails: [[]],
      contact_numbers: [[]],
      dob: ['', Validators.required],
      login_email: ['', [Validators.email, Validators.required]],
      password: ['', Validators.required],
      postcode: [''],
      door_no: [''],
      residential_address: ['', Validators.required],
      city: [''],
      state: [''],
      country_id: ['', Validators.required],
      image: [''],
    });


  }

  ngOnChanges() {
    if (this.isEdit) {
      this.vendorForm.controls['password'].setValidators(null);
      this.vendorForm.controls['password'].updateValueAndValidity();
      this.vendorForm.controls['password'].disable();

      this.vendorForm.patchValue(this.vendorData);
      this.vendorForm.controls['dob'].setValue(new Date(this.vendorData.dob));
      this.vendorForm.controls['contact_emails'].setValue(this.vendorData.contact_emails?.split(','));
      this.vendorForm.controls['contact_numbers'].setValue(this.vendorData.contact_numbers?.split(','));

    } else {
      this.vendorForm.reset();
    }
  }

  saveVendor() {
    if (!this.vendorForm.valid) {
      Object.values(this.vendorForm.controls).forEach((control) => {
        control.markAsTouched();
        control.updateValueAndValidity();
      });
      return;
    }
    if (this.vendorForm.value.contact_emails?.length) {
      this.vendorForm.value.contact_emails = this.vendorForm.value.contact_emails.join(',');
    }
    if (this.vendorForm.value.contact_numbers?.length) {
      this.vendorForm.value.contact_numbers = this.vendorForm.value.contact_numbers.join(',');
    }
    const FormData = createFormData(this.vendorForm.value);

    this.loaderService.show();
    const url = this.vendorForm.value.id ? `users/${this.vendorForm.value.id}` : 'users';
    this.apiService.post(url, FormData).subscribe({
      next: (res: any) => {
        this.vendorDialog = false;
        this.vendorDialogChange.emit(false);
        this.save.emit(res.data);
        this.messageService.add({
          severity: 'success',
          summary: 'Successful',
          detail: `${this.isEdit ? 'Updated' : 'Added'} Vendor`,
          life: 3000,
        });
      },
      error: (err: any) => {
        this.loaderService.hide();
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: err.error.message,
          life: 3000,
        });
      },
      complete: () => {
        this.loaderService.hide();
      },
    });
  }

  closeDialog() {
    this.vendorDialog = false;
    this.vendorDialogChange.emit(false);
  }

  onFileSelected(event: Event) {
    const file = (event.target as HTMLInputElement).files?.[0];
    if (file) {
      this.vendorForm.controls['image'].setValue(file);
      const reader = new FileReader();
      reader.onload = () => {
        this.vendorData.image_url = reader.result as string; // preview image
      };
      reader.readAsDataURL(file);

      // TODO: call API to upload
    }
  }

  // suggest email value after typing like @gmail.com @yoahoo.com @hotmail.com
  searchEmail(event: any) {
    let check = event.query.includes('@');
    if (!check) {
      this.emails = [
        `${event.query}@gmail.com`,
        `${event.query}@yahoo.com`,
        `${event.query}@outlook.com`,
        `${event.query}@icloud.com`,
      ];
    } else {
      this.emails = [
        `${event.query}`,
      ];
    }
  }

  searchNumber(event: any) {
    this.numbers = [
      `${event.query}`,
    ];
  }
}
