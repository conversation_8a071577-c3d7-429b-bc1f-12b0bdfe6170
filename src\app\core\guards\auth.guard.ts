import { inject } from '@angular/core';
import { CanActivateFn, Router, UrlTree } from '@angular/router';
import { AuthService } from '../services/authentication.service';
import { catchError, map } from 'rxjs/operators';
import { Observable, of } from 'rxjs';

export const authGuard: CanActivateFn = (route, state): Observable<boolean> => {
  const authService = inject(AuthService);
  const router = inject(Router);

  if (!authService.isAdmin()) {
    router.navigate(['/auth/login']);
    return of(false);
  }

  return authService.refresh().pipe(
    map((res) => {
      if (res && authService.isAdmin()) {
        return true;
      }
      router.navigate(['/auth/login']);
      return false;
    }),
  );
};
