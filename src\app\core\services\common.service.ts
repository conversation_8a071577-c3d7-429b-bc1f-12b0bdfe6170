import { inject, Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';
import { ApiService } from './api.service';
import { LoaderService } from './loader.service';
import { MessageService } from 'primeng/api';

@Injectable({
    providedIn: 'root',
})
export class CommonService {
    private apiService = inject(ApiService);

    constructor(private http: HttpClient) { }

    lookupPostcode(postcode: string) {
        this.apiService.get(`postcodes/find/${postcode}`).subscribe({
            next: (res: any) => {
                return res;
            },
            error: (err: any) => {
                return err;
            },
            complete: () => {  },
        });
    }

}