<p-table #dt [value]="siteSettings" [rows]="10" editMode="row" [rowHover]="true" dataKey="id"
    [tableStyle]="{ 'min-width': '70rem' }" [scrollable]="true" scrollHeight="80vh"
    [globalFilterFields]="['name', 'value']">
    <ng-template #caption>
        <p-toolbar [style]="{'border': 'none','padding': '0'}">
            <ng-template #start>
                <p class="font-medium font-bold text-2xl">Site Settings</p>
            </ng-template>
            <ng-template #end>
                <!-- search -->
                <p-iconfield class="mr-2">
                    <p-inputicon styleClass="pi pi-search" />
                    <input pInputText type="text" (input)="onGlobalFilter(dt, $event)" placeholder="Search..." />
                </p-iconfield>
                <!-- Load Settings -->
                @if(authService._superman || authService._permissions?.site_settings?.actions?.load_settings) {
                <p-button label="Load Settings" icon="pi pi-cog" class="mr-2" severity="primary"
                    (click)="fetchLoadSiteSettings()" />
                }
                <!-- Refresh -->
                <p-button label="Refresh" icon="pi pi-refresh" class="mr-2" severity="secondary"
                    (click)="fetchSiteSettings()" />
            </ng-template>
        </p-toolbar>
    </ng-template>
    <ng-template #header>
        <tr>
            <th pSortableColumn="name" style="width: 20rem">Name <p-sortIcon field="name" /></th>
            <th pSortableColumn="value">Value <p-sortIcon field="value" /></th>
            <th>Actions</th>
        </tr>
    </ng-template>
    <ng-template #body let-siteSetting let-editing="editing" let-ri="rowIndex">
        <tr [pEditableRow]="siteSetting">
            <td style="width: 20rem">{{ siteSetting.name }}</td>
            <td class="break-all">
                <p-cellEditor>
                    <ng-template #input>

                        @if (siteSetting.data_type == 'string') {
                        <input class="w-full" pInputText type="text" [(ngModel)]="siteSetting.value" required />
                        }

                        @if (siteSetting.data_type == 'int') {
                        <input class="w-full" pInputText type="number" [(ngModel)]="siteSetting.value" required />
                        }

                        @if (siteSetting.data_type == 'double') {
                        <input class="w-full" pInputText type="number" [(ngModel)]="siteSetting.value" required />
                        }

                        @if (siteSetting.data_type == 'text') {
                        <textarea class="w-full" pInputTextarea [(ngModel)]="siteSetting.value" required></textarea>
                        }

                        @if (siteSetting.data_type == 'enum') {
                        <p-select [options]="siteSetting.expected_values?.split(',')" [(ngModel)]="siteSetting.value"
                            placeholder="Select an option" [appendTo]="'body'" required fluid>
                            <ng-template let-option pTemplate="item">
                                {{ option }}
                            </ng-template>
                        </p-select>
                        }

                        @if (siteSetting.data_type == 'boolean') {
                        <p-toggleSwitch [ngModel]="siteSetting.value ? true : false" />
                        }
                    </ng-template>
                    <ng-template #output>
                        {{ siteSetting.value }}
                    </ng-template>
                </p-cellEditor>
            </td>
            <td class="flex gap-2">
                @if (authService._superman || authService._permissions?.site_settings?.actions?.edit) {
                <button *ngIf="!editing" pButton pRipple type="button" pInitEditableRow icon="pi pi-pencil"
                    (click)="onRowEditInit(siteSetting)" rounded outlined severity="primary"></button>
                <button *ngIf="editing" pButton pRipple type="button" pSaveEditableRow icon="pi pi-check"
                    (click)="onRowEditSave(siteSetting)" rounded outlined severity="success"></button>
                <button *ngIf="editing" pButton pRipple type="button" pCancelEditableRow icon="pi pi-times"
                    (click)="onRowEditCancel(siteSetting, ri)" rounded outlined severity="danger"></button>
                } @else {
                <p-tag severity="danger" value="Not Allowed" />
                }
            </td>
        </tr>
    </ng-template>
</p-table>