import { FormBuilder, FormGroup } from "@angular/forms";

export function createPermissionGroup(fb: FormBuilder): FormGroup {
    return fb.group({
        businesses: fb.group({
            actions: fb.group({
                list: [false],
                edit: [false],
                add: [false],
                delete: [false]
            }),
            connect_account: fb.group({
                view: [false],
                add: [false],
                edit: [false],
                generate: [false],

                load_money: [false],
                debit_money: [false],

                add_external_account: [false],
                edit_external_account: [false],
                delete_external_account: [false],

                add_financial_account: [false],
                add_address_financial_account: [false],
                credit_money_financial_account: [false],
                fund_transfer_financial_account: [false],
                view_balance_financial_account: [false],
                delete_financial_account: [false],

                add_bank_account: [false],
                edit_bank_account: [false],
                fund_transfer_bank_account: [false],
                delete_bank_account: [false],
            }),
            transactions: fb.group({
                view: [false],
            }),
            receive_credit: fb.group({
                view: [false],
            })
        }),
        vendors: fb.group({
            actions: fb.group({
                list: [false],
                edit: [false],
                add: [false],
                delete: [false]
            })
        }),
        transactions: fb.group({
            actions: fb.group({
                list: [false],
            })
        }),
        receive_credit: fb.group({
            actions: fb.group({
                list: [false],
            })
        }),
        country: fb.group({
            actions: fb.group({
                list: [false],
                edit: [false],
                add: [false],
                delete: [false]
            })
        }),
        admins: fb.group({
            actions: fb.group({
                list: [false],
                edit: [false],
                add: [false],
                delete: [false]
            })
        }),
        roles: fb.group({
            actions: fb.group({
                list: [false],
                edit: [false],
                add: [false],
                delete: [false]
            })
        }),
        site_settings: fb.group({
            actions: fb.group({
                list: [false],
                load_settings: [false],
                edit: [false],
            })
        })
    });
}