import { Component, inject, OnInit } from '@angular/core';
import { PrimengModules } from '../../../core/utils/primeng';
import { ActivatedRoute, Router } from '@angular/router';
import { ApiService } from '../../../core/services/api.service';
import { LoaderService } from '../../../core/services/loader.service';
import { BusinessDetailsComponent } from '../business-details/business-details.component';
import { AddBusinessComponent } from '../add-business/add-business.component';
import { ConfirmationService, MessageService } from 'primeng/api';
import { ConnectAccountComponent } from '../connect-account/connect-account.component';
import { AuthService } from '../../../core/services/authentication.service';
import { BusinessTransactionsComponent } from '../business-transactions/business-transactions.component';
import { BusinessReceiveCreditComponent } from '../business-receive-credit/business-receive-credit.component';
import { BusinessFeesChargesComponent } from '../business-fees-charges/business-fees-charges.component';

@Component({
  selector: 'app-view-business',
  standalone: true,
  imports: [PrimengModules, BusinessDetailsComponent, AddBusinessComponent, ConnectAccountComponent
    , BusinessTransactionsComponent, BusinessReceiveCreditComponent,
    BusinessFeesChargesComponent
  ],
  templateUrl: './view-business.component.html',
  styleUrl: './view-business.component.scss',
  providers: [ConfirmationService],
})
export class ViewBusinessComponent implements OnInit {
  private activeRoute = inject(ActivatedRoute);
  private apiService = inject(ApiService);
  private loaderService = inject(LoaderService);
  private messageService = inject(MessageService);
  private confirmationService = inject(ConfirmationService);
  public router = inject(Router);
  public authService = inject(AuthService);

  activeTab: number = 0; // default tab
  businessId: any = null;
  business: any = null;
  businessDialog = false;
  isEdit = false;
  items: any[] = []

  constructor() {
    this.activeRoute.params.subscribe((params: any) => { this.businessId = params.id; });
    this.fetchBusiness();
    this.getItems();
  }

  ngOnInit() { }
  getItems() {
    this.items = [
      {
        label: 'Edit',
        icon: 'pi pi-fw pi-pencil',
        visible: (this.authService._superman || this.authService._permissions?.businesses?.actions?.edit) || false,
        command: () => {
          this.isEdit = true;
          this.businessDialog = true;
        },
      },
      {
        label: 'Refresh',
        icon: 'pi pi-fw pi-refresh',

        command: () => {
          this.fetchBusiness();
        },
      },
      {
        label: 'Delete',
        icon: 'pi pi-fw pi-trash',
        visible: (this.authService._superman || this.authService._permissions?.businesses?.actions?.delete) || false,
        command: () => {
          this.deleteBusiness();
        },
      },
    ];
  }

  fetchBusiness() {
    this.loaderService.show();
    this.apiService.get(`businesses/${this.businessId}`).subscribe({
      next: (res: any) => { this.business = res; this.fetchCompanyPeoples(); },
      error: (err: any) => { this.loaderService.hide(); },
      complete: () => { this.loaderService.hide(); },
    });
  }

  fetchCompanyPeoples() {
    this.loaderService.show();
    this.apiService.get(`businesses/${this.businessId}/fetch-company-peoples`).subscribe({
      next: (res: any) => { this.business['company_peoples'] = res; },
      error: (err: any) => { this.loaderService.hide(); },
      complete: () => { this.loaderService.hide(); },
    });
  }

  handleSave(event: any) {
    this.businessDialog = false;
    this.isEdit = false;
    this.fetchBusiness();
  }

  deleteBusiness() {
    this.confirmationService.confirm({
      message: `Are you sure you want to delete ${this.business.business_name}?`,
      header: 'Confirm',
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        this.loaderService.show();
        this.apiService.delete(`businesses/${this.businessId}`).subscribe({
          next: (res: any) => {
            this.messageService.add({ severity: 'success', summary: 'Successful', detail: `${this.business.business_name} deleted`, life: 3000, });
            this.router.navigate(['/business']);
          },
          error: (err: any) => { this.loaderService.hide(); },
          complete: () => { this.loaderService.hide(); },
        });
      },
    });
  }
}
