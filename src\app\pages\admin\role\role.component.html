<p-toolbar styleClass="mb-6" [style]="{'border': 'none','padding': '0','background': 'none'}">
  <ng-template #start>
    <div class="flex flex-col">
      <p class="font-bold text-2xl !m-0">Roles & Permission</p>
      <p class="text-md text-gray-600">Manage your roles</p>
    </div>
  </ng-template>

  <ng-template #end>
    <p-button class="mr-2" outlined pTooltip="Fund Transfer" tooltipPosition="top" size="large">
      <i class="fa-solid fa-file-pdf" style="color: #ff0000"></i>
    </p-button>
    <p-button class="mr-2" outlined pTooltip="Fund Transfer" tooltipPosition="top" size="large">
      <i class="fa-solid fa-file-excel" style="color: #008000"></i>
    </p-button>
    <!-- print -->
    <p-button class="mr-2" outlined pTooltip="Fund Transfer" tooltipPosition="top" size="large">
      <i class="fa-solid fa-print" style="color: #000000"></i>
    </p-button>

    @if(authService._superman || authService._permissions?.roles?.actions?.add) {
    <p-button label="New" icon="pi pi-plus" class="mr-2" (click)="openDialogCreateRole()" />
    }
  </ng-template>
</p-toolbar>

<div class="bg-white p-2 border border-gray-200 rounded-md">
  <p-table #dt [value]="roles" [rows]="5" [paginator]="true" [resetPageOnSort]="false" [globalFilterFields]="['title']"
    [lazy]="true" (onLazyLoad)="onLazyLoad($event)" [rowHover]="true" dataKey="id" [totalRecords]="totalRecords"
    [scrollable]="true" scrollHeight="80vh"
    currentPageReportTemplate="Showing {first} to {last} of {totalRecords} roles" [showCurrentPageReport]="true"
    [rowsPerPageOptions]="[5,10, 20, 30]">
    <ng-template #caption>
      <p-toolbar [style]="{'border': 'none','padding': '0'}">
        <ng-template #start>
          <p-iconfield class="mr-2">
            <p-inputicon styleClass="pi pi-search" />
            <input pInputText type="text" [formControl]="search" placeholder="Search..." />
          </p-iconfield>
        </ng-template>

        <ng-template #end>
          <p-button icon="pi pi-filter" class="mr-2" />
        </ng-template>
      </p-toolbar>

    </ng-template>
    <ng-template #header>
      <tr>
        <th pSortableColumn="name">
          Role Name
          <p-sortIcon field="name" />
        </th>
        <th pSortableColumn="description">
          Description
          <p-sortIcon field="description" />
        </th>
        <th>Actions</th>
      </tr>
    </ng-template>
    <ng-template #body let-role>
      <tr>
        <td>{{ role.name }}</td>
        <td>{{ role.description }}</td>
        <td>
          @if(authService._superman || authService._permissions?.roles?.actions?.edit) {
          <p-button icon="pi pi-pen-to-square" class="mr-2" [outlined]="true" severity="info"
            (click)=" openDialogEditRole(role)" />
          }
          <!-- 
        @if(authService._superman || authService._permissions?.roles?.actions?.delete) {
        <p-button icon="pi pi-trash" severity="danger" [rounded]="true" [outlined]="true" (click)="deleteRole(role)" />
        } -->
        </td>
      </tr>
    </ng-template>
    <ng-template #emptymessage>
      <tr>
        <td colspan="5" style="text-align: center !important;">No records found</td>
      </tr>
    </ng-template>
  </p-table>
</div>

<p-dialog header="{{ roleForm.get('id')?.value ? 'Edit' : 'Add' }} Role" [(visible)]="roleDialog" [autoZIndex]="true"
  position="center" (onHide)="closeDialog()" styleClass="!w-full md:!w-[50rem] lg:!w-[60rem]" [modal]="true">
  <ng-template #content>
    <form [formGroup]="roleForm" (ngSubmit)="saveRole()">
      <div class="grid grid-cols-1 md:grid-cols-1 gap-4">
        <div>
          <label for="title" class="required-label block font-bold mb-3">Role Name</label>
          <input type="text" aria-label="Role Name" pInputText id="name" formControlName="name" placeholder="Role Name"
            autofocus fluid />
          <app-form-error [control]="roleForm.controls['name']" [controlName]="'Role Name'" [apiErrorType]="'name'" />
        </div>
        <div>
          <label for="title" class="required-label block font-bold mb-3">Description</label>
          <input type="text" aria-label="Role Name" pInputText id="description" formControlName="description"
            placeholder="Description" autofocus fluid />
          <app-form-error [control]="roleForm.controls['description']" [controlName]="'Description'"
            [apiErrorType]="'description'" />
        </div>
      </div>
      <!-- Permissions Section -->
      <div class="mt-6">
        <h3 class="font-bold text-lg text-gray-800">Permissions</h3>
        <p class="text-sm text-gray-600">Select the permissions for this role</p>
      </div>
      <app-permissions [(permissions)]="roleForm.value.permissions"></app-permissions>
      <button type="submit" style="display: none"></button>
    </form>
  </ng-template>

  <ng-template #footer>
    <p-button label="Cancel" icon="pi pi-times" text (click)="closeDialog()" />
    <p-button label="Save" icon="pi pi-check" type="submit" (click)="saveRole()" />
  </ng-template>
</p-dialog>

<p-confirmdialog [style]="{ width: '450px' }" />