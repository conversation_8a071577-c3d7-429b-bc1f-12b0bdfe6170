<div [formGroup]="permissionsForm" class="space-y-6 mt-6">
  <!-- Roles -->
  <div class="flex flex-col bg-white border-l-4 border-amber-500 rounded-lg shadow p-5 hover:shadow-lg transition">
    <div class="flex items-center justify-between mb-4">
      <div class="flex items-center gap-2">
        <i class="pi pi-lock text-amber-500 text-2xl"></i>
        <span class="font-bold text-lg text-gray-800">Roles</span>
      </div>
    </div>
    <div formGroupName="roles" class="flex gap-6 flex-wrap">
      <div formGroupName="actions" class="flex flex-col sm:flex-row gap-4">
        <div class="flex items-center gap-2">
          <p-checkbox formControlName="list" binary="true" inputId="roles_list"></p-checkbox>
          <label class="text-gray-800" for="roles_list">List</label>
        </div>
        <div class="flex items-center gap-2">
          <p-checkbox formControlName="add" binary="true" inputId="roles_add"></p-checkbox>
          <label class="text-gray-800" for="roles_add">Add</label>
        </div>
        <div class="flex items-center gap-2">
          <p-checkbox formControlName="edit" binary="true" inputId="roles_edit"></p-checkbox>
          <label class="text-gray-800" for="roles_edit">Edit</label>
        </div>
        <div class="flex items-center gap-2">
          <p-checkbox formControlName="delete" binary="true" inputId="roles_delete"></p-checkbox>
          <label class="text-gray-800" for="roles_delete">Delete</label>
        </div>
      </div>
    </div>
  </div>
  <!-- Businesses -->
  <div class="flex flex-col bg-white border-l-4 border-amber-500 rounded-lg shadow p-5 hover:shadow-lg transition">
    <div class="flex items-center justify-between mb-4">
      <div class="flex items-center gap-2">
        <i class="pi pi-briefcase text-amber-500 text-2xl"></i>
        <span class="font-bold text-lg text-gray-800">Businesses</span>
      </div>
    </div>
    <div formGroupName="businesses" class="flex flex-col gap-4">
      <div formGroupName="actions" class="flex flex-wrap gap-6">
        <div class="flex items-center gap-2">
          <p-checkbox formControlName="list" binary="true" inputId="businesses_list"></p-checkbox>
          <label class="text-gray-800" for="businesses_list">List</label>
        </div>
        <div class="flex items-center gap-2">
          <p-checkbox formControlName="add" binary="true" inputId="businesses_add"></p-checkbox>
          <label class="text-gray-800" for="businesses_add">Add</label>
        </div>
        <div class="flex items-center gap-2">
          <p-checkbox formControlName="edit" binary="true" inputId="businesses_edit"></p-checkbox>
          <label class="text-gray-800" for="businesses_edit">Edit</label>
        </div>
        <div class="flex items-center gap-2">
          <p-checkbox formControlName="delete" binary="true" inputId="businesses_delete"></p-checkbox>
          <label class="text-gray-800" for="businesses_delete">Delete</label>
        </div>
      </div>
      <!-- Sub Permissions: Connect Account -->
      @if (permissionsForm.get('businesses.actions.list')?.value) {
      <div formGroupName="connect_account"
        class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2 gap-3 mt-4 bg-amber-50 border-l-2 border-amber-400 rounded p-3">
        <div class="font-bold text-lg text-gray-800">
          <i class="pi pi-link text-amber-500 text-2xl"></i>
          Connect Account :-
        </div>
        <div class="flex items-center gap-2">
          <p-checkbox formControlName="view" binary="true" inputId="connect_account_view"></p-checkbox>
          <label class="text-gray-800" for="connect_account_view">View</label>
        </div>
        <div class="flex items-center gap-2">
          <p-checkbox formControlName="add" binary="true" inputId="connect_account_add"></p-checkbox>
          <label class="text-gray-800" for="connect_account_add">Add</label>
        </div>
        <div class="flex items-center gap-2">
          <p-checkbox formControlName="edit" binary="true" inputId="connect_account_edit"></p-checkbox>
          <label class="text-gray-800" for="connect_account_edit">Edit</label>
        </div>
        <div class="flex items-center gap-2">
          <p-checkbox formControlName="generate" binary="true" inputId="connect_account_generate"></p-checkbox>
          <label class="text-gray-800" for="connect_account_generate">Generate</label>
        </div>

        <!-- load_money -->
        <div class="flex items-center gap-2">
          <p-checkbox formControlName="load_money" binary="true" inputId="connect_account_load_money"></p-checkbox>
          <label class="text-gray-800" for="connect_account_load_money">Load Money</label>
        </div>
        <!-- debit_money -->
        <div class="flex items-center gap-2">
          <p-checkbox formControlName="debit_money" binary="true" inputId="connect_account_debit_money"></p-checkbox>
          <label class="text-gray-800" for="connect_account_debit_money">Debit Money</label>
        </div>
        <!-- add_external_account -->
        <div class="flex items-center gap-2">
          <p-checkbox formControlName="add_external_account" binary="true"
            inputId="connect_account_add_external_account"></p-checkbox>
          <label class="text-gray-800" for="connect_account_add_external_account">Add External
            Account</label>
        </div>
        <!-- edit_external_account -->
        <div class="flex items-center gap-2">
          <p-checkbox formControlName="edit_external_account" binary="true"
            inputId="connect_account_edit_external_account"></p-checkbox>
          <label class="text-gray-800" for="connect_account_edit_external_account">Edit External
            Account</label>
        </div>
        <!-- delete_external_account -->
        <div class="flex items-center gap-2">
          <p-checkbox formControlName="delete_external_account" binary="true"
            inputId="connect_account_delete_external_account"></p-checkbox>
          <label class="text-gray-800" for="connect_account_delete_external_account">Delete External
            Account</label>
        </div>
        <!-- add_financial_account -->
        <div class="flex items-center gap-2">
          <p-checkbox formControlName="add_financial_account" binary="true"
            inputId="connect_account_add_financial_account"></p-checkbox>
          <label class="text-gray-800" for="connect_account_add_financial_account">Add Financial
            Account</label>
        </div>
        <!-- add_address_financial_account -->
        <div class="flex items-center gap-2">
          <p-checkbox formControlName="add_address_financial_account" binary="true"
            inputId="connect_account_add_address_financial_account"></p-checkbox>
          <label class="text-gray-800" for="connect_account_add_address_financial_account">Add Address
            Financial Account</label>
        </div>
        <!-- credit_money_financial_account -->
        <div class="flex items-center gap-2">
          <p-checkbox formControlName="credit_money_financial_account" binary="true"
            inputId="connect_account_credit_money_financial_account"></p-checkbox>
          <label class="text-gray-800" for="connect_account_credit_money_financial_account">Credit Money
            Financial Account</label>
        </div>
        <!-- fund_transfer_financial_account -->
        <div class="flex items-center gap-2">
          <p-checkbox formControlName="fund_transfer_financial_account" binary="true"
            inputId="connect_account_fund_transfer_financial_account"></p-checkbox>
          <label class="text-gray-800" for="connect_account_fund_transfer_financial_account">Fund Transfer
            Financial Account</label>
        </div>
        <!-- view_balance_financial_account -->
        <div class="flex items-center gap-2">
          <p-checkbox formControlName="view_balance_financial_account" binary="true"
            inputId="connect_account_view_balance_financial_account"></p-checkbox>
          <label class="text-gray-800" for="connect_account_view_balance_financial_account">View Balance
            Financial Account</label>
        </div>
        <!-- delete_financial_account -->
        <div class="flex items-center gap-2">
          <p-checkbox formControlName="delete_financial_account" binary="true"
            inputId="connect_account_delete_financial_account"></p-checkbox>
          <label class="text-gray-800" for="connect_account_delete_financial_account">Delete Financial
            Account</label>
        </div>

        <!-- add_bank_account -->
        <div class="flex items-center gap-2">
          <p-checkbox formControlName="add_bank_account" binary="true"
            inputId="connect_account_add_bank_account"></p-checkbox>
          <label class="text-gray-800" for="connect_account_add_bank_account">Add Bank Account</label>
        </div>
        <!-- edit_bank_account -->
        <div class="flex items-center gap-2">
          <p-checkbox formControlName="edit_bank_account" binary="true"
            inputId="connect_account_edit_bank_account"></p-checkbox>
          <label class="text-gray-800" for="connect_account_edit_bank_account">Edit Bank Account</label>
        </div>
        <!-- fund_transfer_bank_account -->
        <div class="flex items-center gap-2">
          <p-checkbox formControlName="fund_transfer_bank_account" binary="true"
            inputId="connect_account_fund_transfer_bank_account"></p-checkbox>
          <label class="text-gray-800" for="connect_account_fund_transfer_bank_account">Fund Transfer Bank
            Account</label>
        </div>
        <!-- delete_bank_account -->
        <div class="flex items-center gap-2">
          <p-checkbox formControlName="delete_bank_account" binary="true"
            inputId="connect_account_delete_bank_account"></p-checkbox>
          <label class="text-gray-800" for="connect_account_delete_bank_account">Delete Bank
            Account</label>
        </div>
      </div>
      }
      <!-- Sub Permissions: Transactions -->
      @if (permissionsForm.get('businesses.actions.list')?.value) {
      <div formGroupName="transactions"
        class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2 gap-3 mt-4 bg-amber-50 border-l-2 border-amber-400 rounded p-3">
        <div class="font-bold text-lg text-gray-800">
          <i class="pi pi-money-bill text-amber-500 text-2xl"></i>
          Transactions :-
        </div>
        <div class="flex items-center gap-2">
          <p-checkbox formControlName="view" binary="true" inputId="transactions_view"></p-checkbox>
          <label class="text-gray-800" for="transactions_view">View</label>
        </div>
      </div>
      }

      <!-- Sub Permissions: Receive Credit -->
      @if (permissionsForm.get('businesses.actions.list')?.value) {
      <div formGroupName="receive_credit"
        class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2 gap-3 mt-4 bg-amber-50 border-l-2 border-amber-400 rounded p-3">
        <div class="font-bold text-lg text-gray-800">
          <i class="pi pi-credit-card text-amber-500 text-2xl"></i>
          Receive Credit :-
        </div>
        <div class="flex items-center gap-2">
          <p-checkbox formControlName="view" binary="true" inputId="receive_credit_view"></p-checkbox>
          <label class="text-gray-800" for="receive_credit_view">View</label>
        </div>
      </div>
      }
    </div>
  </div>
  <!-- Vendors -->
  <div class="flex flex-col bg-white border-l-4 border-amber-500 rounded-lg shadow p-5 hover:shadow-lg transition">
    <div class="flex items-center justify-between mb-4">
      <div class="flex items-center gap-2">
        <i class="pi pi-users text-amber-500 text-2xl"></i>
        <span class="font-bold text-lg text-gray-800">Vendors</span>
      </div>
    </div>
    <div formGroupName="vendors" class="flex gap-6 flex-wrap">
      <div formGroupName="actions" class="flex flex-col sm:flex-row gap-4">
        <div class="flex items-center gap-2">
          <p-checkbox formControlName="list" binary="true" inputId="vendors_list"></p-checkbox>
          <label class="text-gray-800" for="vendors_list">List</label>
        </div>
        <div class="flex items-center gap-2">
          <p-checkbox formControlName="add" binary="true" inputId="vendors_add"></p-checkbox>
          <label class="text-gray-800" for="vendors_add">Add</label>
        </div>
        <div class="flex items-center gap-2">
          <p-checkbox formControlName="edit" binary="true" inputId="vendors_edit"></p-checkbox>
          <label class="text-gray-800" for="vendors_edit">Edit</label>
        </div>
        <div class="flex items-center gap-2">
          <p-checkbox formControlName="delete" binary="true" inputId="vendors_delete"></p-checkbox>
          <label class="text-gray-800" for="vendors_delete">Delete</label>
        </div>
      </div>
    </div>
  </div>
  <!-- Transactions -->
  <div class="flex flex-col bg-white border-l-4 border-amber-500 rounded-lg shadow p-5 hover:shadow-lg transition">
    <div class="flex items-center justify-between mb-4">
      <div class="flex items-center gap-2">
        <i class="pi pi-money-bill text-amber-500 text-2xl"></i>
        <span class="font-bold text-lg text-gray-800">Transactions</span>
      </div>
    </div>
    <div formGroupName="transactions" class="flex gap-6 flex-wrap">
      <div formGroupName="actions" class="flex flex-col sm:flex-row gap-4">
        <div class="flex items-center gap-2">
          <p-checkbox formControlName="list" binary="true" inputId="transactions_list"></p-checkbox>
          <label class="text-gray-800" for="transactions_list">List</label>
        </div>
      </div>
    </div>
  </div>
  <!-- Receive Credit -->
  <div class="flex flex-col bg-white border-l-4 border-amber-500 rounded-lg shadow p-5 hover:shadow-lg transition">
    <div class="flex items-center justify-between mb-4">
      <div class="flex items-center gap-2">
        <i class="pi pi-credit-card text-amber-500 text-2xl"></i>
        <span class="font-bold text-lg text-gray-800">Receive Credit</span>
      </div>
    </div>
    <div formGroupName="receive_credit" class="flex gap-6 flex-wrap">
      <div formGroupName="actions" class="flex flex-col sm:flex-row gap-4">
        <div class="flex items-center gap-2">
          <p-checkbox formControlName="list" binary="true" inputId="receive_credit_list"></p-checkbox>
          <label class="text-gray-800" for="receive_credit_list" class="text-gray-800">List</label>
        </div>
      </div>
    </div>
  </div>
  <!-- Country -->
  <div class="flex flex-col bg-white border-l-4 border-amber-500 rounded-lg shadow p-5 hover:shadow-lg transition">
    <div class="flex items-center justify-between mb-4">
      <div class="flex items-center gap-2">
        <i class="pi pi-globe text-amber-500 text-2xl"></i>
        <span class="font-bold text-lg text-gray-800">Country</span>
      </div>
    </div>
    <div formGroupName="country" class="flex gap-6 flex-wrap">
      <div formGroupName="actions" class="flex flex-col sm:flex-row gap-4">
        <div class="flex items-center gap-2">
          <p-checkbox formControlName="list" binary="true" inputId="country_list"></p-checkbox>
          <label class="text-gray-800" for="country_list">List</label>
        </div>
        <div class="flex items-center gap-2">
          <p-checkbox formControlName="add" binary="true" inputId="country_add"></p-checkbox>
          <label class="text-gray-800" for="country_add">Add</label>
        </div>
        <div class="flex items-center gap-2">
          <p-checkbox formControlName="edit" binary="true" inputId="country_edit"></p-checkbox>
          <label class="text-gray-800" for="country_edit">Edit</label>
        </div>
        <div class="flex items-center gap-2">
          <p-checkbox formControlName="delete" binary="true" inputId="country_delete"></p-checkbox>
          <label class="text-gray-800" for="country_delete">Delete</label>
        </div>
      </div>
    </div>
  </div>
  <!-- Admins -->
  <div class="flex flex-col bg-white border-l-4 border-amber-500 rounded-lg shadow p-5 hover:shadow-lg transition">
    <div class="flex items-center justify-between mb-4">
      <div class="flex items-center gap-2">
        <i class="pi pi-user text-amber-500 text-2xl"></i>
        <span class="font-bold text-lg text-gray-800">Admins</span>
      </div>
    </div>
    <div formGroupName="admins" class="flex gap-6 flex-wrap">
      <div formGroupName="actions" class="flex flex-col sm:flex-row gap-4">
        <div class="flex items-center gap-2">
          <p-checkbox formControlName="list" binary="true" inputId="admins_list"></p-checkbox>
          <label class="text-gray-800" for="admins_list">List</label>
        </div>
        <div class="flex items-center gap-2">
          <p-checkbox formControlName="add" binary="true" inputId="admins_add"></p-checkbox>
          <label class="text-gray-800" for="admins_add">Add</label>
        </div>
        <div class="flex items-center gap-2">
          <p-checkbox formControlName="edit" binary="true" inputId="admins_edit"></p-checkbox>
          <label class="text-gray-800" for="admins_edit">Edit</label>
        </div>
        <div class="flex items-center gap-2">
          <p-checkbox formControlName="delete" binary="true" inputId="admins_delete"></p-checkbox>
          <label class="text-gray-800" for="admins_delete">Delete</label>
        </div>
      </div>
    </div>
  </div>
  <!-- Site Settings -->
  <div class="flex flex-col bg-white border-l-4 border-amber-500 rounded-lg shadow p-5 hover:shadow-lg transition">
    <div class="flex items-center justify-between mb-4">
      <div class="flex items-center gap-2">
        <i class="pi pi-cog text-amber-500 text-2xl"></i>
        <span class="font-bold text-lg text-gray-800">Site Settings</span>
      </div>
    </div>
    <div formGroupName="site_settings" class="flex gap-6 flex-wrap">
      <div formGroupName="actions" class="flex flex-col sm:flex-row gap-4">
        <div class="flex items-center gap-2">
          <p-checkbox formControlName="list" binary="true" inputId="site_settings_list"></p-checkbox>
          <label class="text-gray-800" for="site_settings_list">List</label>
        </div>
        <div class="flex items-center gap-2">
          <p-checkbox formControlName="edit" binary="true" inputId="site_settings_edit"></p-checkbox>
          <label class="text-gray-800" for="site_settings_edit">Edit</label>
        </div>
        <div class="flex items-center gap-2">
          <p-checkbox formControlName="load_settings" binary="true" inputId="site_settings_load_settings"></p-checkbox>
          <label class="text-gray-800" for="site_settings_load_settings">Load Settings</label>
        </div>
      </div>
    </div>
  </div>
</div>