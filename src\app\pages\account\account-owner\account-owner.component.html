<div class="card">
    @if (accountData) {
        <div class="border-b rounded-lg">
            <div class="mx-auto px-2 sm:px-4 lg:px-6">
                <div class="py-6">
                    <div class="flex items-center justify-between flex-col sm:flex-row">
                        <div class="flex items-center space-x-4">
                            <div
                                class="w-22 h-22 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                                <i class="pi pi-building text-white" style="font-size: 3rem"></i>
                            </div>
                            <div>
                                <h1 class="font-bold">
                                    {{ accountData?.business_profile?.name }}
                                </h1>
                                <div class="flex items-center space-x-2 mt-1">
                                    <span class="text-md">{{ getCountryName(accountData?.country) }}</span>
                                    <p-tag [value]="accountData?.business_type" severity="info" class="text-md">
                                    </p-tag>
                                    <p-tag [value]="accountData?.account_balace?.livemode ? 'Live Mode': 'Test Mode'"
                                        [severity]="accountData?.account_balace?.livemode? 'success': 'warning'  "
                                        class="text-md">
                                    </p-tag>
                                </div>
                            </div>
                        </div>
                        <div class="flex items-center space-x-3 mt-4 sm:mt-4">
                            @if (accountData?.charges_enabled && accountData?.payouts_enabled) {
                                <p-button icon="pi pi-check" label="Account Active" severity="success" />
                            } @else {
                                <p-button icon="pi pi-exclamation-triangle" label="Account Setup Required"
                                    severity="warn" />
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Account Status Banner -->
        <!-- <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        @if (accountData?.charges_enabled && accountData?.payouts_enabled) {
        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
            <div class="flex items-center">
                <i class="pi pi-check-circle text-green-500 mr-3"></i>
                <div>
                    <h3 class=" font-medium text-green-800">Account Active</h3>
                    <p class=" text-green-700">Your account is fully activated and ready to accept payments.</p>
                </div>
            </div>
        </div>
        } @else {
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div class="flex items-center">
                <i class="pi pi-exclamation-triangle text-yellow-500 mr-3"></i>
                <div>
                    <h3 class=" font-medium text-yellow-800">Account Setup Required</h3>
                    <p class=" text-yellow-700">Complete your account setup to start accepting payments.</p>
                </div>
            </div>
        </div>
        }
    </div> -->

        <!-- Main Content -->
        <div class="max-w-9xl mx-auto py-4">
            <p-tabs [(value)]="activeTab" scrollable [showNavigators]="true">
                <p-tablist>
                    <p-tab [value]="0">Details</p-tab>
                    <p-tab [value]="1">Account</p-tab>
                    <p-tab [value]="2">Transactions</p-tab>
                </p-tablist>
                <p-tabpanels>
                    <p-tabpanel [value]="0">
                        <div class="grid grid-cols-1 mb-4">
                            <!-- Balance Cards -->
                            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                                <div class="rounded-lg border border-gray-200 p-6 hover:shadow-lg transition-shadow">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <p class="font-medium">Available Balance</p>
                                            <p class="text-2xl font-bold text-green-600">
                                                {{ (accountData?.account_balace?.available[0].amount / 100) | currency : "GBP" : "symbol" : "1.2-2" }}
                                            </p>
                                        </div>
                                        <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                                            <i class="pi pi-wallet text-green-600"></i>
                                        </div>
                                    </div>
                                </div>

                                <div class="rounded-lg border border-gray-200 p-6 hover:shadow-lg transition-shadow">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <p class="font-medium">Incoming Balance</p>
                                            <p class="text-2xl font-bold text-yellow-600">
                                                {{ (accountData?.account_balace?.pending[0].amount / 100)| currency : "GBP" : "symbol" : "1.2-2" }}
                                            </p>
                                        </div>
                                        <div
                                            class="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center">
                                            <i class="pi pi-clock text-yellow-600"></i>
                                        </div>
                                    </div>
                                </div>

                                <div class="rounded-lg border border-gray-200 p-6 hover:shadow-lg transition-shadow">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <p class="font-medium">Connect Reserved</p>
                                            <p class="text-2xl font-bold text-blue-600">
                                                {{ (accountData?.account_balace?.connect_reserved[0].amount / 100) | currency : "GBP" : "symbol" : "1.2-2" }}
                                            </p>
                                        </div>
                                        <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                            <i class="pi pi-shield text-blue-600"></i>
                                        </div>
                                    </div>
                                </div>

                                <div class="rounded-lg border border-gray-200 p-6 hover:shadow-lg transition-shadow">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <p class="font-medium">Available Instant Payouts</p>
                                            <p class="text-2xl font-bold text-green-600">
                                                {{ (accountData?.account_balace?.instant_available[0].amount / 100) | currency : "GBP" : "symbol" : "1.2-2" }}
                                            </p>
                                        </div>
                                        <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                            <i class="pi pi-money-bill text-green-600"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                            <!-- Left Column - Account Overview -->
                            <div class="lg:col-span-2 space-y-6">
                                <!-- Account Details Panel -->
                                <div class="rounded-lg border border-gray-200 hover:shadow-lg transition-shadow">
                                    <div class="px-6 py-4 border-b border-gray-200">
                                        <h2 class="text-lg font-semibold">Account Details</h2>
                                    </div>
                                    <div class="p-6">
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                            <div class="space-y-4">
                                                <div>
                                                    <label class="font-medium">Account ID</label>
                                                    <div class="flex items-center space-x-2 mt-1">
                                                        <code
                                                            class="bg-gray-100 px-2 py-1 rounded font-mono">{{ accountData?.id || "--" }}</code>
                                                    </div>
                                                </div>

                                                <div>
                                                    <label class="font-medium">Business Type</label>
                                                    <p class="mt-1 capitalize">
                                                        {{ accountData?.business_type || "--" }}
                                                    </p>
                                                </div>

                                                <div>
                                                    <label class="font-medium">Email</label>
                                                    <p class="mt-1">{{ accountData?.email || "--" }}</p>
                                                </div>

                                                <div>
                                                    <label class="font-medium">Default Currency</label>
                                                    <p class="mt-1 uppercase">
                                                        {{ accountData?.default_currency || "--"     }}
                                                    </p>
                                                </div>
                                            </div>

                                            <div class="space-y-4">
                                                <div>
                                                    <label class="font-medium">Company Structure</label>
                                                    <p class="mt-1 capitalize">
                                                        {{  accountData?.company?.structure ?
                                                    accountData?.company?.structure.replace("_", " ") : "--"
                                                    }}
                                                    </p>
                                                </div>

                                                <div>
                                                    <label class="font-medium">MCC Code</label>
                                                    <p class="mt-1">
                                                        {{ accountData?.business_profile?.mcc || "--" }}
                                                    </p>
                                                </div>

                                                <div>
                                                    <label class="font-medium">Timezone</label>
                                                    <p class="mt-1">
                                                        {{ accountData?.settings?.dashboard?.timezone || "--" }}
                                                    </p>
                                                </div>

                                                <div>
                                                    <label class="font-medium">Statement Descriptor</label>
                                                    <p class="mt-1">
                                                        {{ accountData?.settings?.payments?.statement_descriptor || "Not set" }}
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Business Profile Panel -->
                                <div class="rounded-lg border border-gray-200 hover:shadow-lg transition-shadow">
                                    <div class="px-6 py-4 border-b border-gray-200">
                                        <h2 class="text-lg font-semibold">Business Profile</h2>
                                    </div>
                                    <div class="p-6">
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                            <div class="space-y-4">
                                                <div>
                                                    <label class="font-medium">Business Name</label>
                                                    <p class="mt-1">
                                                        {{ accountData?.business_profile?.name || "--" }}
                                                    </p>
                                                </div>

                                                <div>
                                                    <label class="font-medium">Website</label>
                                                    <a [href]="accountData?.business_profile?.url" target="_blank"
                                                        class="text-blue-600 hover:text-blue-800 mt-1 block">
                                                        {{ accountData?.business_profile?.url || "--" }}
                                                    </a>
                                                </div>

                                                <div>
                                                    <label class="font-medium">Support Email</label>
                                                    <a [href]="'mailto:' + accountData?.business_profile?.support_email"
                                                        class="text-blue-600 hover:text-blue-800 mt-1 block">
                                                        {{ accountData?.business_profile?.support_email || "--" }}
                                                    </a>
                                                </div>

                                                <div>
                                                    <label class="font-medium">Support Phone</label>
                                                    <p class="mt-1">
                                                        {{ accountData?.business_profile?.support_phone || "--" }}
                                                    </p>
                                                </div>
                                            </div>

                                            <div class="space-y-4">
                                                <div>
                                                    <label class="font-medium">Support Address</label>
                                                    <div class="mt-1">
                                                        <p>
                                                            {{ accountData?.business_profile?.support_address?.line1 || "--" }}
                                                        </p>
                                                        @if (accountData?.business_profile?.support_address?.line2) {
                                                            <p>
                                                                {{ accountData?.business_profile?.support_address?.line2 }}
                                                            </p>
                                                        }
                                                        <p>
                                                            @if (accountData?.business_profile?.support_address?.city) {
                                                            {{ accountData?.business_profile?.support_address?.city  }},
                                                            }
                                                            @if (accountData?.business_profile?.support_address?.state) {
                                                            {{ accountData?.business_profile?.support_address?.state }}
                                                            }
                                                            @if (accountData?.business_profile?.support_address?.postal_code) {
                                                            {{ accountData?.business_profile?.support_address?.postal_code }}
                                                            }
                                                        </p>
                                                        <p>
                                                            {{ getCountryName(accountData?.business_profile?.support_address?.country) }}
                                                        </p>
                                                    </div>
                                                </div>

                                                <div>
                                                    <label class="font-medium">Support URL</label>
                                                    <a [href]="accountData?.business_profile?.support_url"
                                                        target="_blank"
                                                        class="text-blue-600 hover:text-blue-800 mt-1 block">
                                                        {{ accountData?.business_profile?.support_url || "--" }}
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Card Issuing Section -->
                                <div class="rounded-lg border border-gray-200 hover:shadow-lg transition-shadow">
                                    <div class="px-6 py-4 border-b border-gray-200">
                                        <h2 class="text-lg font-semibold">Card Issuing</h2>
                                    </div>
                                    <div class="p-6">
                                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                            <div>
                                                <label class="font-medium">Available Balance</label>
                                                <p class="text-xl font-bold mt-1">
                                                    {{ (accountData?.account_balace?.issuing.available[0].amount / 100) | currency : "GBP" : "symbol" : "1.2-2" }}
                                                </p>
                                            </div>

                                            <div>
                                                <label class="font-medium">ToS Acceptance Date</label>
                                                <p class="mt-1">
                                                    {{ accountData?.settings?.card_issuing?.tos_acceptance?.date | date : "dd-MM-yyyy HH:mm" }}
                                                </p>
                                            </div>

                                            <div>
                                                <label class="font-medium">ToS IP Address</label>
                                                <p class="mt-1 font-mono">
                                                    {{ accountData?.settings?.card_issuing?.tos_acceptance?.ip || "--" }}
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Refund and Dispute Prefunding -->
                                <div class="rounded-lg border border-gray-200 hover:shadow-lg transition-shadow">
                                    <div class="px-6 py-4 border-b border-gray-200">
                                        <h2 class="text-lg font-semibold">
                                            Refund & Dispute Prefunding
                                        </h2>
                                    </div>
                                    <div class="p-6">
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                            <div>
                                                <label class="font-medium">Available</label>
                                                <p class="text-xl font-bold text-green-600 mt-1">
                                                    {{ (accountData?.account_balace?.refund_and_dispute_prefunding?.available[0].amount / 100) | currency : "GBP" : "symbol" : "1.2-2" }}
                                                </p>
                                            </div>

                                            <div>
                                                <label class="font-medium">Pending</label>
                                                <p class="text-xl font-bold text-yellow-600 mt-1">
                                                    {{ (accountData?.account_balace?.refund_and_dispute_prefunding?.pending[0].amount /100) | currency : "GBP" : "symbol" : "1.2-2" }}
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Right Column - Capabilities & Settings -->
                            <div class="space-y-6">
                                <!-- Capabilities Overview -->
                                <div class="rounded-lg border border-gray-200 hover:shadow-lg transition-shadow">
                                    <div class="px-6 py-4 border-b border-gray-200">
                                        <h2 class="text-lg font-semibold">Capabilities</h2>
                                    </div>
                                    <div class="p-6">
                                        <div class="grid grid-cols-3 gap-4 mb-6">
                                            <div class="text-center">
                                                <div class="text-2xl font-bold text-green-600">
                                                    {{ getActiveCapabilities() }}
                                                </div>
                                                <div class="text-xs">Active</div>
                                            </div>
                                            <div class="text-center">
                                                <div class="text-2xl font-bold text-yellow-600">
                                                    {{ getPendingCapabilities() }}
                                                </div>
                                                <div class="text-xs">Pending</div>
                                            </div>
                                            <div class="text-center">
                                                <div class="text-2xl font-bold text-red-600">
                                                    {{ getInactiveCapabilities() }}
                                                </div>
                                                <div class="text-xs">Inactive</div>
                                            </div>
                                        </div>

                                        <div class="space-y-3">
                                            @for (capability of getCapabilityKeys(); track capability) {
                                                <div class="flex items-center justify-between">
                                                    <span
                                                        class="capitalize">{{ formatCapabilityName(capability) }}</span>
                                                    <p-tag [value]="accountData?.capabilities[capability] | titlecase"
                                                        [severity]="getCapabilityStatus(capability)" class="text-xs">
                                                    </p-tag>
                                                </div>
                                            }
                                        </div>
                                    </div>
                                </div>

                                <!-- Payout Settings -->
                                <div class="rounded-lg border border-gray-200 hover:shadow-lg transition-shadow">
                                    <div class="px-6 py-4 border-b border-gray-200">
                                        <h2 class="text-lg font-semibold">Payout Settings</h2>
                                    </div>
                                    <div class="p-6 space-y-4">
                                        <div>
                                            <label class="font-medium">Schedule</label>
                                            <p class="mt-1 capitalize">
                                                {{ accountData?.settings?.payouts?.schedule?.interval }}
                                                (
                                                {{ accountData?.settings?.payouts?.schedule?.delay_days }}
                                                day delay
                                                )
                                            </p>
                                        </div>

                                        <div>
                                            <label class="font-medium">Debit Negative Balances</label>
                                            <div class="mt-1">
                                                <p-tag
                                                    [value]="accountData?.settings?.payouts?.debit_negative_balances ? 'Enabled' : 'Disabled'"
                                                    [severity]="accountData?.settings?.payouts?.debit_negative_balances? 'success': 'danger'"
                                                    class="text-xs">
                                                </p-tag>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Branding -->
                                <!-- <div class=" rounded-lg border border-gray-200">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h2 class="text-lg font-semibold ">Branding</h2>
                    </div>
                    <div class="p-6 space-y-4">
                        <div>
                            <label class=" font-medium ">Primary Color</label>
                            <div class="flex items-center space-x-2 mt-1">
                                <div class="w-6 h-6 rounded border border-gray-300"
                                    [style.background-color]="accountData?.settings.branding.primary_color">
                                </div>
                                <span class=" ">{{ accountData?.settings.branding.primary_color
                                    }}</span>
                            </div>
                        </div>

                        <div>
                            <label class=" font-medium ">Secondary Color</label>
                            <div class="flex items-center space-x-2 mt-1">
                                <div class="w-6 h-6 rounded border border-gray-300"
                                    [style.background-color]="accountData?.settings.branding.secondary_color">
                                </div>
                                <span class=" ">{{ accountData?.settings.branding.secondary_color
                                    }}</span>
                            </div>
                        </div>

                        <div>
                            <label class=" font-medium ">Logo</label>
                            <p class="  mt-1 font-mono">{{ accountData?.settings.branding.logo }}
                            </p>
                        </div>

                        <div>
                            <label class=" font-medium ">Icon</label>
                            <p class="  mt-1 font-mono">{{ accountData?.settings.branding.icon }}
                            </p>
                        </div>
                    </div>
                </div> -->

                                <!-- BACS Debit Settings -->
                                <div class="rounded-lg border border-gray-200 hover:shadow-lg transition-shadow">
                                    <div class="px-6 py-4 border-b border-gray-200">
                                        <h2 class="text-lg font-semibold">BACS Debit</h2>
                                    </div>
                                    <div class="p-6 space-y-4">
                                        <div>
                                            <label class="font-medium">Display Name</label>
                                            <p class="mt-1">
                                                {{ accountData?.settings?.bacs_debit_payments?.display_name }}
                                            </p>
                                        </div>

                                        <div>
                                            <label class="font-medium">Service User Number</label>
                                            <p class="mt-1 font-mono">
                                                {{ accountData?.settings?.bacs_debit_payments?.service_user_number }}
                                            </p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Account Status -->
                                <div class="rounded-lg border border-gray-200 hover:shadow-lg transition-shadow">
                                    <div class="px-6 py-4 border-b border-gray-200">
                                        <h2 class="text-lg font-semibold">Account Status</h2>
                                    </div>
                                    <div class="p-6 space-y-4">
                                        <div class="flex items-center justify-between">
                                            <span class=" ">Charges Enabled</span>
                                            <p-tag [value]="accountData?.charges_enabled ? 'Yes' : 'No'"
                                                [severity]=" accountData?.charges_enabled ? 'success' : 'danger'"
                                                class="text-xs">
                                            </p-tag>
                                        </div>

                                        <div class="flex items-center justify-between">
                                            <span class=" ">Payouts Enabled</span>
                                            <p-tag [value]="accountData?.payouts_enabled ? 'Yes' : 'No'"
                                                [severity]="accountData?.payouts_enabled ? 'success' : 'danger'"
                                                class="text-xs">
                                            </p-tag>
                                        </div>

                                        <div class="flex items-center justify-between">
                                            <span class=" ">Details Submitted</span>
                                            <p-tag [value]="accountData?.details_submitted ? 'Yes' : 'No'"
                                                [severity]="accountData?.details_submitted ? 'success' : 'danger'"
                                                class="text-xs">
                                            </p-tag>
                                        </div>

                                        <div class="flex items-center justify-between">
                                            <span class=" ">Live Mode</span>
                                            <p-tag [value]="accountData?.account_balace?.livemode ? 'Live' : 'Test'"
                                                [severity]="accountData?.account_balace?.livemode  ? 'success'  : 'warning' "
                                                class="text-xs">
                                            </p-tag>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </p-tabpanel>

                    <p-tabpanel [value]="1">
                        <p-panel header="Financial Details" [toggleable]="true" styleClass="mt-4">
                            @if (!is_financial_accounts_loading) {
                                <p-table #dt [value]="financial_accounts" [rowHover]="true" dataKey="id"
                                    [tableStyle]="{ 'min-width': '70rem' }" [scrollable]="true" scrollHeight="60vh">
                                    <ng-template #caption>
                                        <p-toolbar [style]="{ border: 'none', padding: '0' }">
                                            <ng-template #start>
                                                <p class="font-medium font-bold text-2xl">
                                                    Financial Accounts
                                                </p>
                                            </ng-template>

                                            <ng-template #end>
                                                @if ((authService._superman ||authService._permissions?.businesses?.connect_account?.add_financial_account)) {
                                                    <p-button label="Add Financial Connection" icon="pi pi-plus"
                                                        severity="secondary" class="mr-2"
                                                        (click)="openDialogToAddFinancialConnection()" />
                                                }
                                                <p-button label="Hard Refresh" icon="pi pi-refresh" severity="help"
                                                    class="mr-2" (click)="fetchFinancialConnectionsHardRefresh()" />
                                            </ng-template>
                                        </p-toolbar>
                                    </ng-template>
                                    <ng-template #header>
                                        <tr>
                                            <th pSortableColumn="account_holder_name" pFrozenColumn>
                                                Account Holder Name
                                                <p-sortIcon field="account_holder_name" />
                                            </th>
                                            <th pSortableColumn="account_number">
                                                Account Number
                                                <p-sortIcon field="account_number" />
                                            </th>
                                            <th pSortableColumn="routing_number">
                                                Sort Code
                                                <p-sortIcon field="routing_number" />
                                            </th>
                                            <th style="text-align: center" pSortableColumn="balance">
                                                Balance
                                                <p-sortIcon field="balance" />
                                            </th>
                                            <th pSortableColumn="status">
                                                Status
                                                <p-sortIcon field="status" />
                                            </th>
                                            <th>Action</th>
                                        </tr>
                                    </ng-template>
                                    <ng-template #body let-financialAccount>
                                        <tr>
                                            @if (financialAccount?.financial_addresses?.account_holder_name) {
                                                <td pFrozenColumn>
                                                    {{ financialAccount?.financial_addresses?.account_holder_name }}
                                                    <span *ngIf="financialAccount?.metadata?.nickname">
                                                        ({{ financialAccount?.metadata?.nickname }})
                                                    </span>
                                                </td>
                                            } @else {
                                                <td pFrozenColumn>--</td>
                                            }
                                            <td>
                                                {{ financialAccount?.financial_addresses?.account_number ||"--" }}
                                            </td>
                                            <td>
                                                {{ financialAccount?.financial_addresses?.routing_number ||"--" }}
                                            </td>
                                            <td style="text-align: center">
                                                @if( (authService._superman ||
                                                            authService._permissions?.businesses?.connect_account?.view_balance_financial_account)
                                                        && financialAccount?.financial_addresses){
                                                    <p-button raised rounded icon="pi pi-eye" severity="info"
                                                        pTooltip="View Balance" tooltipPosition="top"
                                                        (click)="viewFinancialAccountBalance(financialAccount)" />
                                                } @else {
                                                    <p-tag severity="danger" value="Not Available" />
                                                }
                                            </td>
                                            <td>
                                                @if (financialAccount?.financial_addresses) {
                                                    <p-button
                                                        [label]="financialAccount?.financial_addresses?.status| titlecase"
                                                        raised rounded
                                                        [severity]="getSeverity(financialAccount?.financial_addresses?.status)" />
                                                } @else { -- }
                                            </td>
                                            <td>
                                                @if (!financialAccount?.financial_addresses) {
                                                    @if((authService._superman ||
                                                        authService._permissions?.businesses?.connect_account?.add_address_financial_account)){
                                                        <p-button label="Address" icon="pi pi-plus" severity="info"
                                                            class="mr-2" outlined
                                                            (click)="openDialogToAddAddress(financialAccount)" />
                                                    } } @else {
                                                    <!-- @if((authService._superman ||
                                        authService._permissions?.businesses?.connect_account?.credit_money_financial_account)){
                                        <p-button icon="pi pi-credit-card" severity="success" class="mr-2"
                                            [rounded]="true" outlined pTooltip="Credit Money" tooltipPosition="top"
                                            (click)="OpenCreditMoneyModal(financialAccount)">
                                        </p-button>
                                        } -->
                                                    @if((authService._superman ||
                                                        authService._permissions?.businesses?.connect_account?.fund_transfer_financial_account)){
                                                        <p-button severity="info" class="mr-2" [rounded]="true" outlined
                                                            pTooltip="Fund Transfer" tooltipPosition="top"
                                                            (click)="fundTransferFromFinancialAccount(financialAccount)">
                                                            <i class="fa-solid fa-money-bill-transfer"
                                                                style="color: #2d89ef"></i>
                                                        </p-button>
                                                    } @if((authService._superman ||
                                                            authService._permissions?.businesses?.connect_account?.fund_transfer_financial_account)){
                                                        <p-button severity="success" class="mr-2" [rounded]="true"
                                                            outlined pTooltip="Other Business Account Transfer"
                                                            (click)="transferBetweenOtherBusinessAccounts(financialAccount)">
                                                            <i class="fa-solid fa-right-left"
                                                                style="color: #28a745"></i>
                                                        </p-button>
                                                    } } @if((authService._superman ||
                                                            authService._permissions?.businesses?.connect_account?.delete_financial_account)){
                                                    <p-button icon="pi pi-trash" class="mr-2" pTooltip="Delete"
                                                        tooltipPosition="top" severity="danger" [rounded]="true"
                                                        [outlined]="true"
                                                        (click)="deleteFinancialConnection(financialAccount)" />
                                                }
                                                <p-button pTooltip="Download Statement" class="mr-2" [rounded]="true"
                                                    [outlined]="true" tooltipPosition="top" icon="pi pi-download"
                                                    severity="info" class="mr-2"
                                                    (click)="downloadStatementOp.toggle($event)" />
                                                <p-popover #downloadStatementOp [dismissable]="true"
                                                    (onHide)="statementDateRange.reset()">
                                                    <div class="flex flex-col gap-4 w-[25rem]">
                                                        <p-toolbar [style]="{ border: 'none', padding: '0' }">
                                                            <ng-template #start>
                                                                <p class="font-medium font-bold text-xl">
                                                                    Download Statement ({{
                                                                    financialAccount?.metadata?.nickname
                                                                    }})
                                                                </p>
                                                            </ng-template>
                                                            <ng-template #end>
                                                                <p-button icon="pi pi-times" text
                                                                    (click)="downloadStatementOp.hide()" />
                                                            </ng-template>
                                                        </p-toolbar>
                                                        <form (ngSubmit)="downloadStatement(financialAccount)">
                                                            <div class="grid grid-cols-1 md:grid-cols-1 gap-4">
                                                                <div>
                                                                    <label for="date_range"
                                                                        class="required-label block font-bold mb-3">Date
                                                                        Range
                                                                    </label>
                                                                    <p-datepicker [formControl]="statementDateRange"
                                                                        selectionMode="range" inputId="date_range"
                                                                        [showClear]="true" styleClass="w-full"
                                                                        placeholder="Select a date range"
                                                                        dateFormat="dd-mm-yy" [appendTo]="'body'" />
                                                                    <app-form-error [control]="statementDateRange"
                                                                        [controlName]="'Date Range'"
                                                                        [apiErrorType]="'date_range'" />
                                                                </div>
                                                                <button type="submit" style="display: none"></button>
                                                            </div>
                                                            <div
                                                                class="flex justify-end gap-2 mt-6 border-t border-gray-200 pt-2">
                                                                <p-button label="Cancel" icon="pi pi-times" text
                                                                    (click)="downloadStatementOp.toggle($event)" />
                                                                <p-button label="Download" icon="pi pi-download"
                                                                    (click)="downloadStatement(financialAccount)" />
                                                            </div>
                                                        </form>
                                                    </div>
                                                </p-popover>
                                            </td>
                                        </tr>
                                    </ng-template>

                                    <ng-template #emptymessage>
                                        <tr>
                                            <td colspan="4" style="text-align: center !important">
                                                No records found
                                            </td>
                                        </tr>
                                    </ng-template>
                                </p-table>
                                } @else {
                                    <p-table [value]="loderData" responsiveLayout="scroll">
                                        <ng-template #body let-product>
                                            <tr>
                                                <td>
                                                    <p-skeleton />
                                                </td>
                                                <td>
                                                    <p-skeleton />
                                                </td>
                                                <td>
                                                    <p-skeleton />
                                                </td>
                                                <td>
                                                    <p-skeleton />
                                                </td>
                                            </tr>
                                        </ng-template>
                                    </p-table>
                                }
                        </p-panel>

                        <p-panel header="Bank Account Details" [toggleable]="true" styleClass="mt-4">
                            @if (!is_bank_accounts_loading) {
                                <p-table #dt [value]="bank_accounts" [rowHover]="true" dataKey="id"
                                    [tableStyle]="{ 'min-width': '60rem' }" [scrollable]="true" scrollHeight="60vh">
                                    <ng-template #caption>
                                        <p-toolbar [style]="{ border: 'none', padding: '0' }">
                                            <ng-template #start>
                                                <p class="font-medium font-bold text-2xl">Bank Accounts</p>
                                            </ng-template>
                                            <ng-template #end>
                                                @if((authService._superman ||
                                                        authService._permissions?.businesses?.connect_account?.add_bank_account)){
                                                    <p-button label="Add Bank Account" icon="pi pi-plus"
                                                        severity="secondary" class="mr-2"
                                                        (click)="openDialogToAddBankAccount()" />
                                                }
                                                <p-button label="Hard Refresh" icon="pi pi-refresh" severity="help"
                                                    class="mr-2" (click)="fetchBankAccountsHardRefresh()" />
                                            </ng-template>
                                        </p-toolbar>
                                    </ng-template>
                                    <ng-template #header>
                                        <tr>
                                            <th pSortableColumn="account_holder_name" pFrozenColumn>
                                                Account Holder Name
                                                <p-sortIcon field="account_holder_name" />
                                            </th>
                                            <th pSortableColumn="bank_name">
                                                Bank Name
                                                <p-sortIcon field="bank_name" />
                                            </th>
                                            <th pSortableColumn="last4">
                                                Account Number
                                                <p-sortIcon field="last4" />
                                            </th>
                                            <th pSortableColumn="sort_code">
                                                Sort Code
                                                <p-sortIcon field="sort_code" />
                                            </th>
                                            <th>Action</th>
                                        </tr>
                                    </ng-template>
                                    <ng-template #body let-bankAccount>
                                        <tr>
                                            <td pFrozenColumn>{{ bankAccount?.account_holder_name }}</td>
                                            <td>{{ bankAccount?.bank_name }}</td>
                                            <td>XXXX{{ bankAccount?.last4 }}</td>
                                            <td>{{ bankAccount?.sort_code }}</td>
                                            <td>
                                                @if (!bankAccount?.archived) { @if((authService._superman ||
                                                        authService._permissions?.businesses?.connect_account?.fund_transfer_bank_account)){
                                                        <p-button severity="info" class="mr-2" [rounded]="true" outlined
                                                            pTooltip="Fund Transfer" tooltipPosition="top" disabled>
                                                            <i class="fa-solid fa-money-bill-transfer"
                                                                style="color: #2d89ef"></i>
                                                        </p-button>
                                                    } @if ((authService._superman ||
                                                            authService._permissions?.businesses?.connect_account?.edit_bank_account))
                                                            {
                                                        <p-button icon="pi pi-pencil" class="mr-2" [rounded]="true"
                                                            [outlined]="true"
                                                            (click)="openDialogToAddBankAccount(bankAccount)" />
                                                    } @if((authService._superman ||
                                                            authService._permissions?.businesses?.connect_account?.delete_bank_account)){
                                                        <p-button icon="pi pi-trash" severity="danger" [rounded]="true"
                                                            [outlined]="true"
                                                            (click)="deleteBankAccount(bankAccount)" />
                                                    } } @else {
                                                    <p-tag severity="danger" value="Archived" />
                                                }
                                            </td>
                                        </tr>
                                    </ng-template>
                                    <ng-template #emptymessage>
                                        <tr>
                                            <td colspan="5" style="text-align: center !important">
                                                No records found
                                            </td>
                                        </tr>
                                    </ng-template>
                                </p-table>
                                } @else {
                                    <p-table [value]="loderData" responsiveLayout="scroll">
                                        <ng-template #body let-product>
                                            <tr>
                                                <td>
                                                    <p-skeleton />
                                                </td>
                                                <td>
                                                    <p-skeleton />
                                                </td>
                                                <td>
                                                    <p-skeleton />
                                                </td>
                                                <td>
                                                    <p-skeleton />
                                                </td>
                                            </tr>
                                        </ng-template>
                                    </p-table>
                                }
                        </p-panel>
                    </p-tabpanel>
                    <p-tabpanel [value]="2">
                        @if (activeTab === 2) {
                            <app-account-transactions [business]="business"></app-account-transactions>
                        }
                    </p-tabpanel>
                </p-tabpanels>
            </p-tabs>
        </div>
        } @else {
            <div class="flex justify-center items-center text-2xl font-bold text-red-500">
                <p>Account not found</p>
            </div>
        }
</div>

<p-confirmdialog [style]="{ width: '450px' }" />
