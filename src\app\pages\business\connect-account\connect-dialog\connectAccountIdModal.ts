import { Component, OnInit, inject } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MessageService } from 'primeng/api';
import { DialogService, DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { PrimengModules } from '../../../../core/utils/primeng';
import { LoaderService } from '../../../../core/services/loader.service';
import { ServerApiService } from '../../../../core/services/server-api.service';

@Component({
    selector: 'app-connect-account-id-modal',
    imports: [PrimengModules],
    template: `
    
    <div class="w-full">
        <form [formGroup]="connectAccountIdModalForm">
            <div class="grid grid-cols-1 md:grid-cols-1 gap-4">
                <div>
                    <label for="account_type" class="required-label block font-bold mb-3">Account Type</label>
                    <p-select [options]="account_types" optionLabel="name" optionValue="id" [showClear]="true"
                        formControlName="account_type" placeholder="Select a account type" [appendTo]="'body'" required
                        fluid />
                    <small class="text-red-500"
                        *ngIf="connectAccountIdModalForm.get('account_type')?.hasError('required') && connectAccountIdModalForm.get('account_type')?.touched">Account
                        Type is required.</small>
                </div>
                <div *ngIf="connectAccountIdModalForm.get('account_type')?.value === 'Other'">
                    <label for="s_account_id" class="required-label block font-bold mb-3">Account ID</label>
                    <input type="text" aria-label="Account ID" pInputText id="s_account_id"
                        formControlName="s_account_id" placeholder="Account ID" required autofocus fluid />
                    <small class="text-red-500"
                        *ngIf="connectAccountIdModalForm.get('s_account_id')?.hasError('required') && connectAccountIdModalForm.get('s_account_id')?.touched">Account
                        ID is required.</small>
                </div>
            </div>
        </form>
    </div>
    <div class="flex justify-end gap-2 mt-6 border-t border-gray-200 pt-2">
     <p-button label="Cancel" icon="pi pi-times" text (click)="close()" />
     <p-button label="Save" icon="pi pi-check" (click)="saveConnectAccountId()" />
   </div>
  `,
    providers: [DialogService],
})
export class ConnectAccountIdModal implements OnInit {
    private fb = inject(FormBuilder);
    private messageService = inject(MessageService);
    private ref = inject(DynamicDialogRef);
    private config = inject(DynamicDialogConfig);
    private loaderService = inject(LoaderService);
    private serverApiService = inject(ServerApiService);
    
    private business: any = null;

    connectAccountIdModalForm!: FormGroup;
    account_types = [{ id: "Default", name: "Default" }, { id: "Other", name: "Other" }];

    constructor() {
        this.business = this.config.data.business;
    }
    ngOnInit() {
        this.createConnectAccountIdModalForm();
    }

    createConnectAccountIdModalForm() {
        this.connectAccountIdModalForm = this.fb.group({
            account_type: ['', Validators.required],
            s_account_id: [''],
        });

        this.connectAccountIdModalForm.get('account_type')?.valueChanges.subscribe((val) => {
            if (val === 'Other') {
                this.connectAccountIdModalForm.get('s_account_id')?.setValidators(Validators.required);
                this.connectAccountIdModalForm.get('s_account_id')?.updateValueAndValidity();
            } else {
                this.connectAccountIdModalForm.get('s_account_id')?.setValidators(null);
                this.connectAccountIdModalForm.get('s_account_id')?.updateValueAndValidity();
            }
        });
    }

    saveConnectAccountId() {
        if (!this.connectAccountIdModalForm.valid) {
            this.connectAccountIdModalForm.markAllAsTouched();
            this.connectAccountIdModalForm.updateValueAndValidity();
            this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Please fill all the required fields', life: 3000, });
            return;
        }

        this.loaderService.show();
        this.serverApiService.post(`connect/connect-accounts/business/${this.business.id}`, { s_account_id: this.connectAccountIdModalForm.value.s_account_id, business_id: this.business.id.toString() }).subscribe({
            next: (res: any) => {
                this.ref.close(res);
            },
            error: (err: any) => {
                this.loaderService.hide();
                this.messageService.add({ severity: 'error', summary: 'Error', detail: err.error.message, life: 3000, });
            },
            complete: () => { this.loaderService.hide(); },
        });
    }

    close() {
        this.ref.close();
    }
}
