import { Component, OnInit, inject } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MessageService } from 'primeng/api';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { DialogService } from 'primeng/dynamicdialog';
import { PrimengModules } from '../../../../core/utils/primeng';
import { LoaderService } from '../../../../core/services/loader.service';
import { ServerApiService } from '../../../../core/services/server-api.service';
import { ApiService } from '../../../../core/services/api.service';
import { HttpParams } from '@angular/common/http';
import { createFormData, filterParams } from '../../../../core/utils/utils';

@Component({
    selector: 'app-fund-transfer-modal',
    imports: [PrimengModules],
    template: `
     
<div class="w-full">
    <form [formGroup]="fundTransferForm" (ngSubmit)="saveFundTransfer()">
        <div class="grid grid-cols-1 md:grid-cols-1 gap-4">
            <!-- business_id -->
            <div>
                <label for="business_id" class="required-label block font-bold mb-3">Business</label>
                <p-select [options]="businesses" [filter]="true"  [loading]="business_loading" optionLabel="name" optionValue="id" [showClear]="true"
                    formControlName="business_id" placeholder="Select a business" [appendTo]="'body'"
                    fluid />
                <app-form-error [control]="fundTransferForm.controls['business_id']" [controlName]="'Business'"
                    [apiErrorType]="'business_id'" />
            </div>
            <!-- financial_account_id -->
            <div>
                <label for="to_financial_account_id" class="required-label block font-bold mb-3">Financial Account</label>
                <p-select [options]="financial_accounts" [filter]="true"   optionValue="id" [showClear]="true"
                    formControlName="to_financial_account_id" placeholder="Select a financial account" [appendTo]="'body'"
                    fluid> 
                    <ng-template #selectedItem let-selectedOption>
                        <div *ngIf="selectedOption; else noSelection" class="flex items-center gap-2">
                            <div>
                                {{selectedOption?.financial_addresses?.account_holder_name}} - 
                                {{selectedOption?.metadata?.nickname}}
                            </div>
                        </div>
                        <ng-template #noSelection>
                            <span class="text-gray-400">Select a financial account</span>
                        </ng-template>
                    </ng-template>
                    <ng-template let-option pTemplate="item">
                         <div class="flex items-center gap-2">
                            <span>{{option.financial_addresses?.account_holder_name}} - {{option.metadata?.nickname}}</span>
                        </div>
                    </ng-template>
                </p-select>
                <app-form-error [control]="fundTransferForm.controls['to_financial_account_id']" [controlName]="'Financial Account'"
                    [apiErrorType]="'to_financial_account_id'" />
            </div>
            <!-- amount -->
            <div>
                <label for="amount" class="required-label block font-bold mb-3">Amount</label>
                <p-inputnumber formControlName="amount" aria-label="Amount" inputId="amount" 
                    placeholder="Amount"  mode="decimal" [minFractionDigits]="2" required fluid />
                <app-form-error [control]="fundTransferForm.controls['amount']" [controlName]="'Amount'"
                    [apiErrorType]="'amount'" />
            </div>
            <!-- description -->
            <div>
                <label for="description" class="required-label block font-bold mb-3">Description</label>
                <input type="text" aria-label="Description" pInputText id="description" formControlName="description"
                    placeholder="Description" required autofocus fluid />
                <app-form-error [control]="fundTransferForm.controls['description']" [controlName]="'Description'"
                    [apiErrorType]="'description'" />
            </div>
            <!-- submit  -->
            <button type="submit" style="display: none"></button>
        </div>
    </form>
</div>
<div class="flex justify-end gap-2 mt-6 border-t border-gray-200 pt-2">
    <p-button label="Cancel" icon="pi pi-times" text (click)="close()" />
    <p-button label="Save" icon="pi pi-check" (click)="saveFundTransfer()" />
</div>
      `,
    providers: [DialogService],
})
export class TransferOtherBusinessAccModal implements OnInit {
    private fb = inject(FormBuilder);
    private messageService = inject(MessageService);
    private ref = inject(DynamicDialogRef);
    private config = inject(DynamicDialogConfig);
    private loaderService = inject(LoaderService);
    private serverApiService = inject(ServerApiService);
    private apiService = inject(ApiService);

    connectAccounts_id: any = null;
    financialAccount_id: any = null;
    business: any = null;
    businesses: any[] = [];
    financial_accounts: any[] = [];

    fundTransferForm!: FormGroup;

    business_loading = false;

    constructor() {
        this.createFundTransferForm();
        this.fetchBusinesses();
    }

    ngOnInit() {
        this.connectAccounts_id = this.config.data.connectAccounts_id;
        this.financialAccount_id = this.config.data.financialAccount_id;
        this.business = this.config.data?.business;
    }

    fetchBusinesses() {
        const httpParams: HttpParams = filterParams({ nopaginate: 1 });
        this.loaderService.show();
        this.apiService.get('businesses/mini', httpParams).subscribe({
            next: (res: any) => {
                if (this.business) {
                    res = res.filter((a: any) => a.id !== this.business.id);
                }
                this.businesses = res;
            },
            error: (err: any) => { this.loaderService.hide(); },
            complete: () => { this.loaderService.hide(); },
        });
    }

    createFundTransferForm() {
        this.fundTransferForm = this.fb.group({
            business_id: ['', Validators.required],
            to_financial_account_id: ['', Validators.required],
            amount: [null, Validators.required],
            description: ['', Validators.required],
        });

        this.fundTransferForm.get('business_id')?.valueChanges.subscribe((val) => {
            if (val) {
                this.financial_accounts = [];
                this.fetchFinancialAccounts(val);
                this.fundTransferForm.get('to_financial_account_id')?.setValue(null);
            } else {
                this.financial_accounts = [];
                this.fundTransferForm.get('to_financial_account_id')?.setValue(null);
            }
        });
    }

    fetchFinancialAccounts(business_id: any) {
        this.financial_accounts = [];
        const business = this.businesses.find((a: any) => a.id === business_id);
        if (!business?.connect_account) {
            this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Connect account not found', life: 3000, });
            return;
        }
        const httpParams: HttpParams = filterParams({ nopaginate: 1 });
        this.business_loading = true;
        this.serverApiService.get(`connect/connect-accounts/${business?.connect_account?.id}/financial-accounts`, httpParams).subscribe({
            next: (res: any) => { this.financial_accounts = res; },
            error: (err: any) => {
                this.business_loading = false;
                this.messageService.add({ severity: 'error', summary: 'Error', detail: err.error.message, life: 3000, });
            },
            complete: () => { this.business_loading = false; },
        });
    }

    saveFundTransfer() {
        if (!this.fundTransferForm.valid) {
            this.fundTransferForm.markAllAsTouched();
            this.fundTransferForm.updateValueAndValidity();
            this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Please fill all the required fields', life: 3000, });
            return;
        }
        const formData = createFormData(this.fundTransferForm.value);
        this.loaderService.show();
        this.serverApiService.post(`connect/connect-accounts/${this.connectAccounts_id}/financial-accounts/${this.financialAccount_id}/transfer-financial-account`, formData).subscribe({
            next: (res: any) => {
                this.ref.close(res);
                this.messageService.add({ severity: 'success', summary: 'Successful', detail: res.message, life: 3000, });
            },
            error: (err: any) => {
                this.loaderService.hide();
                this.messageService.add({ severity: 'error', summary: 'Error', detail: err.error.message, life: 3000, });
            },
            complete: () => { this.loaderService.hide(); },
        });
    }

    close() {
        this.ref.close();
    }
}
