import { Component, inject, OnInit, ViewChild } from '@angular/core';
import { Table, TableLazyLoadEvent, TablePageEvent } from 'primeng/table';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { ConfirmationService, MessageService } from 'primeng/api';
import { ActivatedRoute, Router } from '@angular/router';
import { debounceTime } from 'rxjs';
import { HttpParams } from '@angular/common/http';
import { PrimengModules } from '../../../core/utils/primeng';
import { ApiService } from '../../../core/services/api.service';
import { LoaderService } from '../../../core/services/loader.service';
import { AuthService } from '../../../core/services/authentication.service';
import { DialogService } from 'primeng/dynamicdialog';
import { createFormData, filterParams } from '../../../core/utils/utils';

@Component({
  selector: 'app-franchise-owner',
  imports: [PrimengModules],
  standalone: true,
  templateUrl: './franchise-owner.component.html',
  styleUrl: './franchise-owner.component.scss',
  providers: [ConfirmationService, DialogService],
})
export class FranchiseOwnerComponent implements OnInit {
  private apiService = inject(ApiService);
  private loaderService = inject(LoaderService);
  private fb = inject(FormBuilder);
  private messageService = inject(MessageService);
  private confirmationService = inject(ConfirmationService);
  private router = inject(Router);
  public authService = inject(AuthService);

  @ViewChild('dt') dt!: Table;

  filterForm!: FormGroup;
  franchiseOwnerForm!: FormGroup;
  search = new FormControl('');
  countries: any[] = [];
  totalRecords: number = 0;
  franchiseOwnerDialog = false;
  selectedFranchiseOwnerId: any = null;

  constructor() {
    this.createFranchiseOwnerForm();
    this.filterForm = this.fb.group({
      page: [],
      limit: [],
    });
  }

  ngOnInit() {
    this.search.valueChanges.pipe(debounceTime(400)).subscribe((val) => {
      this.fetchCountries();
    });
  }

  fetchCountries() {
    const httpParams: HttpParams = filterParams({
      ...this.filterForm.value,
      query: this.search.value,
    });
    this.loaderService.show();
    this.apiService.get('franchise_owner', httpParams).subscribe({
      next: (res: any) => {
        this.countries = res.data.countries;
        this.totalRecords = res.totalItems;
      },
      error: (err: any) => {
        this.loaderService.hide();
      },
      complete: () => {
        this.loaderService.hide();
      },
    });
  }

  openDialogCreateFranchiseOwner() {
    this.franchiseOwnerDialog = true;
    this.franchiseOwnerForm.reset();
  }

  openDialogEditFranchiseOwner(franchiseOwner: any) {
    this.franchiseOwnerDialog = true;
    this.franchiseOwnerForm.reset();
    this.franchiseOwnerForm.patchValue(franchiseOwner);
    this.selectedFranchiseOwnerId = franchiseOwner.id;
    this.franchiseOwnerForm.controls['phone_code'].setValue(franchiseOwner.phone_code.replace('+', ''));
  }

  deleteFranchiseOwner(franchiseOwner: any) {
    this.confirmationService.confirm({
      message: 'Are you sure you want to delete ' + franchiseOwner.name + '?',
      header: 'Confirm',
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        this.loaderService.show();
        this.apiService.delete(`franchise_owner/${franchiseOwner.id}`).subscribe({
          next: (res: any) => {
            this.messageService.add({
              severity: 'success',
              summary: 'Successful',
              detail: `${franchiseOwner.name} deleted`,
              life: 3000,
            });
            this.fetchCountries();
          },
          error: (err: any) => {
            this.loaderService.hide();
          },
          complete: () => {
            this.loaderService.hide();
          },
        });
      },
    });
  }

  onLazyLoad(event: any) {
    if (
      event.first >= 0 &&
      event.rows &&
      (this.filterForm.value.page !== event.first / event.rows + 1 ||
        this.filterForm.value.limit !== event.rows)
    ) {
      this.filterForm.controls['page'].setValue(event.first / event.rows + 1, {
        emitEvent: false,
      });
      this.filterForm.controls['limit'].setValue(event.rows, {
        emitEvent: false,
      });
      this.fetchCountries();
    }
    if (event.sortField && event.sortOrder) {
      this.countries.sort((a, b) => {
        if (a[event.sortField] < b[event.sortField]) {
          return event.sortOrder === 1 ? -1 : 1;
        }
        if (a[event.sortField] > b[event.sortField]) {
          return event.sortOrder === 1 ? 1 : -1;
        }
        return 0;
      });
    }
  }

  closeDialog() {
    this.franchiseOwnerDialog = false;
    this.franchiseOwnerForm.reset();
  }

  createFranchiseOwnerForm() {
    this.franchiseOwnerForm = this.fb.group({
      first_name: ['', Validators.required],
      last_name: ['', Validators.required],
      gender: ['', Validators.required],
      date_of_birth: ['', Validators.required],
      email: ['', Validators.required],
      phone_number: ['', Validators.required],
      alternate_phone: [''],
      business_name: ['', Validators.required],
      address_line1: ['', Validators.required],
      address_line2: [''],
      city: ['', Validators.required],
      state: ['', Validators.required],
      country_id: ['', Validators.required],
      postal_code: ['', Validators.required],
      status: ['', Validators.required],
    });
  }

  saveFranchiseOwner() {
    if (!this.franchiseOwnerForm.valid) {
      Object.values(this.franchiseOwnerForm.controls).forEach((control) => {
        control.markAsTouched();
        control.updateValueAndValidity();
      });
      return;
    }
    this.franchiseOwnerForm.value.phone_code = '+' + this.franchiseOwnerForm.value.phone_code;
    const FormData = createFormData(this.franchiseOwnerForm.value);

    if (this.selectedFranchiseOwnerId) {
      this.updateFranchiseOwner(FormData);
    } else {
      this.createFranchiseOwner(FormData);
    }
  }

  createFranchiseOwner(FormData: any) {
    this.loaderService.show();
    const url = 'franchise_owner';
    this.apiService.post(url, FormData).subscribe({
      next: (res: any) => {
        this.franchiseOwnerDialog = false;
        this.fetchCountries();
        this.messageService.add({
          severity: 'success',
          summary: 'Successful',
          detail: 'Added Franchise Owner',
          life: 3000,
        });
        this.franchiseOwnerForm.reset();
      },
      error: (err: any) => {
        this.loaderService.hide();
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: err.error.message,
          life: 3000,
        });
      },
      complete: () => {
        this.loaderService.hide();
      },
    });
  }

  updateFranchiseOwner(FormData: any) {
    this.loaderService.show();
    const url = `franchise_owner/${this.selectedFranchiseOwnerId}`;
    this.apiService.put(url, FormData).subscribe({
      next: (res: any) => {
        this.franchiseOwnerDialog = false;
        this.fetchCountries();
        this.messageService.add({
          severity: 'success',
          summary: 'Successful',
          detail: 'Updated Franchise Owner',
          life: 3000,
        });
        this.franchiseOwnerForm.reset();
        this.selectedFranchiseOwnerId = null;
      },
      error: (err: any) => {
        this.loaderService.hide();
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: err.error.message,
          life: 3000,
        });
      },
      complete: () => {
        this.loaderService.hide();
      },
    });
  }

}
