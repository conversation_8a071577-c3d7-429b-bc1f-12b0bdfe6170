<p-toolbar styleClass="mb-6" [style]="{'border': 'none','padding': '0','background': 'none'}">
  <ng-template #start>
    <div class="flex flex-col">
      <p class="font-bold text-2xl !m-0">Franchise Owner</p>
      <p class="text-md text-gray-600">Manage franchise owners</p>
    </div>
  </ng-template>

  <ng-template #end>
    <p-button class="mr-2" outlined pTooltip="Fund Transfer" tooltipPosition="top" size="large">
      <i class="fa-solid fa-file-pdf" style="color: #ff0000"></i>
    </p-button>
    <p-button class="mr-2" outlined pTooltip="Fund Transfer" tooltipPosition="top" size="large">
      <i class="fa-solid fa-file-excel" style="color: #008000"></i>
    </p-button>
    <!-- print -->
    <p-button class="mr-2" outlined pTooltip="Fund Transfer" tooltipPosition="top" size="large">
      <i class="fa-solid fa-print" style="color: #000000"></i>
    </p-button>

    @if(authService._superman || authService._permissions?.owners?.actions?.add) {
    <p-button label="New" icon="pi pi-plus" class="mr-2" (click)="openDialogCreateFranchiseOwner()" />
    }
  </ng-template>
</p-toolbar>

<div class="bg-white p-2 border border-gray-200 rounded-md">
  <p-table #dt [value]="countries" [rows]="10" [paginator]="true" [resetPageOnSort]="false"
    [globalFilterFields]="['name', 'iso', 'iso3', 'currency_name', 'currency_code', 'currency_symbol']" [lazy]="true"
    (onLazyLoad)="onLazyLoad($event)" [rowHover]="true" dataKey="id" [totalRecords]="totalRecords"
    [tableStyle]="{ 'min-width': '70rem' }" [scrollable]="true" scrollHeight="80vh"
    currentPageReportTemplate="Showing {first} to {last} of {totalRecords} countries" [showCurrentPageReport]="true"
    [rowsPerPageOptions]="[10, 20, 30]">
    <ng-template #caption>
      <p-toolbar [style]="{'border': 'none','padding': '0'}">
        <ng-template #start>
          <p-iconfield class="mr-2">
            <p-inputicon styleClass="pi pi-search" />
            <input pInputText type="text" [formControl]="search" placeholder="Search..." />
          </p-iconfield>
        </ng-template>
        <ng-template #end>
          <p-button icon="pi pi-filter" class="mr-2" />
        </ng-template>
      </p-toolbar>
    </ng-template>
    <ng-template #header>
      <tr>
        <th pSortableColumn="name" pFrozenColumn>
          Name
          <p-sortIcon field="name" />
        </th>
        <th pSortableColumn="iso">
          ISO Code
          <p-sortIcon field="iso" />
        </th>
        <th pSortableColumn="currency_name">
          Currency
          <p-sortIcon field="currency_name" />
        </th>
        <th pSortableColumn="phone_code">
          Phone Code
          <p-sortIcon field="phone_code" />
        </th>
        <th>Actions</th>
      </tr>
    </ng-template>
    <ng-template #body let-owners>
      <tr>
        <td pFrozenColumn>{{ owners.name }}</td>
        <td>{{ owners.iso }} / {{ owners.iso3 }}</td>
        <td>{{ owners.currency_name }}</td>
        <td>
          {{ owners.phone_code }}
        </td>
        <td>
          @if(authService._superman || authService._permissions?.owners?.actions?.edit) {
          <p-button icon="pi pi-pen-to-square" class="mr-2" [outlined]="true" severity="info"
            (click)="openDialogEditFranchiseOwner(owners)" />
          }
          @if(authService._superman || authService._permissions?.owners?.actions?.delete) {
          <p-button icon="pi pi-trash" severity="danger" [outlined]="true" (click)="deleteFranchiseOwner(owners)" />
          }
        </td>
      </tr>
    </ng-template>
    <ng-template #emptymessage>
      <tr>
        <td colspan="5" style="text-align: center !important;">No records found</td>
      </tr>
    </ng-template>
  </p-table>
</div>

<p-dialog [(visible)]="franchiseOwnerDialog" [style]="{ width: '450px' }" [modal]="true"
  header="{{ franchiseOwnerForm.get('id')?.value ? 'Edit' : 'Add' }} Franchise Owner" (onHide)="closeDialog()">
  <ng-template #content>
    <form [formGroup]="franchiseOwnerForm" (ngSubmit)="saveFranchiseOwner()">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label for="name" class="required-label block font-bold mb-3">Country Name</label>
          <input type="text" aria-label="Country Name" pInputText id="name" formControlName="name"
            placeholder="e.g. United Kingdom" autofocus fluid />
          <app-form-error [control]="franchiseOwnerForm.controls['name']" [controlName]="'Country Name'"
            [apiErrorType]="'name'" />
        </div>
        <div>
          <label for="phone_code" class="required-label block font-bold mb-3">Phone Code</label>
          <p-inputnumber formControlName="phone_code" inputId="phone_code" prefix="+" placeholder="e.g. 44" required
            fluid />
          <app-form-error [control]="franchiseOwnerForm.controls['phone_code']" [controlName]="'Phone Code'"
            [apiErrorType]="'phone_code'" />
        </div>
        <div>
          <label for="iso" class="required-label block font-bold mb-3">ISO Code</label>
          <input type="text" aria-label="ISO Code" pInputText id="iso" formControlName="iso" placeholder="e.g. GB"
            autofocus fluid />
          <app-form-error [control]="franchiseOwnerForm.controls['iso']" [controlName]="'ISO Code'"
            [apiErrorType]="'iso'" />
        </div>
        <div>
          <label for="iso3" class="required-label block font-bold mb-3">ISO3 Code</label>
          <input type="text" aria-label="ISO3 Code" pInputText id="iso3" formControlName="iso3" placeholder="e.g. GBR"
            autofocus fluid />
          <app-form-error [control]="franchiseOwnerForm.controls['iso3']" [controlName]="'ISO3 Code'"
            [apiErrorType]="'iso3'" />
        </div>
      </div>
      <div class="grid grid-cols-2 md:grid-cols-3 gap-4 mt-4">
        <div>
          <label for="currency_name" class="required-label block font-bold mb-3">Currency Name</label>
          <input type="text" aria-label="Currency Name" pInputText id="currency_name" formControlName="currency_name"
            placeholder="e.g. Pound" autofocus fluid />
          <app-form-error [control]="franchiseOwnerForm.controls['currency_name']" [controlName]="'Currency Name'"
            [apiErrorType]="'currency_name'" />
        </div>
        <div>
          <label for="currency_code" class="required-label block font-bold mb-3">Currency Code</label>
          <input type="text" aria-label="Currency Code" pInputText id="currency_code" formControlName="currency_code"
            placeholder="e.g. GBP" autofocus fluid />
          <app-form-error [control]="franchiseOwnerForm.controls['currency_code']" [controlName]="'Currency Code'"
            [apiErrorType]="'currency_code'" />
        </div>
        <div>
          <label for="currency_symbol" class="required-label block font-bold mb-3">Currency Symbol</label>
          <input type="text" aria-label="Currency Symbol" pInputText id="currency_symbol"
            formControlName="currency_symbol" placeholder="e.g. £" autofocus fluid />
          <app-form-error [control]="franchiseOwnerForm.controls['currency_symbol']" [controlName]="'Currency Symbol'"
            [apiErrorType]="'currency_symbol'" />
        </div>
        <div>
          <label for="enabled" class="required-label block font-bold mb-3">Enabled</label>
          <p-toggleswitch id="enabled" formControlName="enabled" />
        </div>
      </div>
      <button type="submit" style="display: none"></button>
    </form>
  </ng-template>
  <ng-template #footer>
    <p-button label="Cancel" icon="pi pi-times" text (click)="closeDialog()" />
    <p-button label="Save" icon="pi pi-check" type="submit" (click)="saveFranchiseOwner()" />
  </ng-template>
</p-dialog>

<p-confirmdialog [style]="{ width: '450px' }" />