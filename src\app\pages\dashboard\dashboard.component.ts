import { Component, inject } from '@angular/core';
import { CardModule } from 'primeng/card';
import { ChartModule } from 'primeng/chart';
import { debounceTime, Subscription } from 'rxjs';
import { LoaderService } from '../../core/services/loader.service';
import { ApiService } from '../../core/services/api.service';
import { PrimengModules } from '../../core/utils/primeng';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [PrimengModules],
  templateUrl: './dashboard.component.html',
  styleUrl: './dashboard.component.scss',
})
export class DashboardComponent {
  private loaderService = inject(LoaderService);
  private apiService = inject(ApiService);

  dashboard: any = null;

  constructor() {
    // this.fetchDashboard();
  }

  ngOnInit() {
  }

  fetchDashboard() {
    this.loaderService.show();
    this.apiService.get('dashboard').subscribe({
      next: (res: any) => { this.dashboard = res; },
      error: (err: any) => { this.loaderService.hide(); },
      complete: () => { this.loaderService.hide(); },
    });
  }

  getCategoryColor(category: string) {
    switch (category) {
      case 'received_credit': return 'text-green-700';
      case 'outbound_transfer': return 'text-red-700';
      default: return 'bg-gray-50 ';
    }
  }

  getSeverity(status: string) {
    switch (status) {
      case 'succeeded': return 'success';
      case 'failed': return 'danger';
      default: return 'info';
    }
  }
}
