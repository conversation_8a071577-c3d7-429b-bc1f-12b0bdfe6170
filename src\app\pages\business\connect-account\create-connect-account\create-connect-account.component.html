<p-drawer header="Create connect account" [(visible)]="connectAccountDialog" [autoZIndex]="true" position="right"
    (visibleChange)="connectAccountDialogChange.emit($event)" styleClass="!w-full md:!w-[50rem] lg:!w-[70rem]"
    [modal]="true" [dismissible]="false">
    <ng-template #content>
        <form>
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 mb-4">
                <div [formGroup]="company">
                    <label for="name" class="required-label block font-bold mb-3">Business name</label>
                    <input type="text" aria-label="Business name" pInputText id="name" formControlName="name"
                        placeholder="Business name" autofocus fluid />
                    <app-form-error [control]="company.controls['name']" [controlName]="'Business name'"
                        [apiErrorType]="'name'" />
                </div>
                <div [formGroup]="connectAccountForm">
                    <label for="business_type" class="required-label block font-bold mb-3">Business type</label>
                    <p-select [options]="business_types" optionLabel="name" optionValue="id" [showClear]="true"
                        formControlName="business_type" placeholder="Select a business type" [appendTo]="'body'"
                        fluid />
                    <app-form-error [control]="connectAccountForm.controls['business_type']"
                        [controlName]="'Business type'" [apiErrorType]="'business_type'" />
                </div>
                <div [formGroup]="connectAccountForm">
                    <label for="display_name" class="required-label block font-bold mb-3">Display Name</label>
                    <input type="text" aria-label="Display Name" pInputText id="display_name"
                        formControlName="display_name" placeholder="Display Name" autofocus fluid />
                    <app-form-error [control]="connectAccountForm.controls['display_name']"
                        [controlName]="'Display Name'" [apiErrorType]="'display_name'" />
                </div>
                <div [formGroup]="company">
                    <label for="email" class="required-label block font-bold mb-3">Business Email</label>
                    <input type="text" aria-label="Email" pInputText id="email" formControlName="email"
                        placeholder="Email" autofocus fluid />
                    <app-form-error [control]="company.controls['email']" [controlName]="'Email'"
                        [apiErrorType]="'email'" />
                </div>
                <div [formGroup]="company">
                    <label for="phone_number" class="required-label block font-bold mb-3">Business Phone number</label>
                    <input type="text" aria-label="Phone number" pInputText id="phone_number"
                        formControlName="phone_number" placeholder="Phone number" autofocus fluid />
                    <app-form-error [control]="company.controls['phone_number']" [controlName]="'Phone number'"
                        [apiErrorType]="'phone_number'" />
                </div>
                <div [formGroup]="company">
                    <label for="url" class="required-label block font-bold mb-3">Business url</label>
                    <input type="text" aria-label="Website" pInputText id="url" formControlName="url"
                        placeholder="Website" autofocus fluid />
                    <app-form-error [control]="company.controls['url']" [controlName]="'Website'"
                        [apiErrorType]="'url'" />
                </div>
                <div [formGroup]="company">
                    <label for="mcc" class="required-label block font-bold mb-3">Business category</label>
                    <p-select [options]="mcc_codes" optionLabel="name" optionValue="value" [showClear]="true"
                        formControlName="mcc" placeholder="Select a business category" [appendTo]="'body'" required
                        fluid />
                    <app-form-error [control]="company.controls['mcc']" [controlName]="'Business category'"
                        [apiErrorType]="'mcc'" />
                </div>
                <div [formGroup]="company">
                    <label for="tax_id" class="required-label block font-bold mb-3">Business Tax ID</label>
                    <input type="text" aria-label="Tax ID" pInputText id="tax_id" formControlName="tax_id"
                        placeholder="Tax ID" autofocus fluid />
                    <app-form-error [control]="company.controls['tax_id']" [controlName]="'Tax ID'"
                        [apiErrorType]="'tax_id'" />
                </div>
                <div [formGroup]="connectAccountForm">
                    <label for="country" class="required-label block font-bold mb-3">Country</label>
                    <p-select [options]="countries" optionLabel="name" optionValue="iso" [showClear]="true"
                        formControlName="country" placeholder="Select a country" [appendTo]="'body'" fluid />
                    <app-form-error [control]="connectAccountForm.controls['country']" [controlName]="'Country'"
                        [apiErrorType]="'country'" />
                </div>
                <div [formGroup]="company">
                    <label for="annual_revenue_amount" class="required-label block font-bold mb-3">Annual
                        Revenue</label>
                    <p-inputnumber formControlName="annual_revenue_amount" aria-label="Annual Revenue" inputId="annual_revenue_amount"
                        placeholder="Annual Revenue" mode="decimal" [minFractionDigits]="2" autofocus fluid />
                    <app-form-error [control]="company.controls['annual_revenue_amount']"
                        [controlName]="'Annual Revenue'" [apiErrorType]="'annual_revenue_amount'" />
                </div>
                <div [formGroup]="company">
                    <label for="fiscal_year_end" class="required-label block font-bold mb-3">Fiscal Year End</label>
                    <p-datePicker formControlName="fiscal_year_end" [showIcon]="true" [appendTo]="'body'"
                        styleClass="w-full" dateFormat="dd-mm-yy" />
                    <app-form-error [control]="company.controls['fiscal_year_end']" [controlName]="'Fiscal Year End'"
                        [apiErrorType]="'fiscal_year_end'" />
                </div>
                <div [formGroup]="company">
                    <label for="estimated_worker_count" class="required-label block font-bold mb-3">Estimated Worker
                        Count</label>
                    <input type="number" aria-label="Estimated Worker Count" pInputText id="estimated_worker_count"
                        formControlName="estimated_worker_count" placeholder="Estimated Worker Count" autofocus fluid />
                    <app-form-error [control]="company.controls['estimated_worker_count']"
                        [controlName]="'Estimated Worker Count'" [apiErrorType]="'estimated_worker_count'" />
                </div>
            </div>
            <div class="mt-4">
                <div class="text-lg mb-2">Business Address Details</div>
            </div>
            <div [formGroup]="company">
                <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 mb-4">
                    <div>
                        <label for="line1" class="required-label block font-bold mb-3">Address Line 1</label>
                        <input type="text" aria-label="Address Line 1" pInputText id="line1" formControlName="line1"
                            placeholder="Address Line 1" autofocus fluid />
                        <app-form-error [control]="company.controls['line1']" [controlName]="'Address Line 1'"
                            [apiErrorType]="'line1'" />
                    </div>
                    <div>
                        <label for="line2" class="block font-bold mb-3">Address Line 2</label>
                        <input type="text" aria-label="Address Line 2" pInputText id="line2" formControlName="line2"
                            placeholder="Address Line 2" autofocus fluid />
                        <app-form-error [control]="company.controls['line2']" [controlName]="'Address Line 2'"
                            [apiErrorType]="'line2'" />
                    </div>
                    <div>
                        <label for="city" class="required-label block font-bold mb-3">City</label>
                        <input type="text" aria-label="City" pInputText id="city" formControlName="city"
                            placeholder="City" autofocus fluid />
                        <app-form-error [control]="company.controls['city']" [controlName]="'City'"
                            [apiErrorType]="'city'" />
                    </div>
                    <div>
                        <label for="state" class="required-label block font-bold mb-3">State</label>
                        <input type="text" aria-label="State" pInputText id="state" formControlName="state"
                            placeholder="State" autofocus fluid />
                        <app-form-error [control]="company.controls['state']" [controlName]="'State'"
                            [apiErrorType]="'state'" />
                    </div>
                    <div>
                        <label for="postal_code" class="required-label block font-bold mb-3">Postal Code</label>
                        <input type="text" aria-label="Postal Code" pInputText id="postal_code"
                            formControlName="postal_code" placeholder="Postal Code" autofocus fluid />
                        <app-form-error [control]="company.controls['postal_code']" [controlName]="'Postal Code'"
                            [apiErrorType]="'postal_code'" />
                    </div>
                </div>
            </div>
            <div class="mt-4">
                <div class="text-lg mb-2">Account Opener Details</div>
            </div>
            <div [formGroup]="account_opener">
                <div class="grid grid-cols-1 sm:grid-cols-2 sm:grid-cols-2 md:grid-cols-3 gap-4 mb-4">
                    <div>
                        <label for="first_name" class="required-label block font-bold mb-3">First Name</label>
                        <input type="text" aria-label="First Name" pInputText id="first_name"
                            formControlName="first_name" placeholder="First Name" autofocus fluid />
                        <app-form-error [control]="account_opener.controls['first_name']" [controlName]="'First Name'"
                            [apiErrorType]="'first_name'" />
                    </div>
                    <div>
                        <label for="last_name" class="required-label block font-bold mb-3">Last Name</label>
                        <input type="text" aria-label="Last Name" pInputText id="last_name" formControlName="last_name"
                            placeholder="Last Name" autofocus fluid />
                        <app-form-error [control]="account_opener.controls['last_name']" [controlName]="'Last Name'"
                            [apiErrorType]="'last_name'" />
                    </div>
                    <div>
                        <label for="email" class="required-label block font-bold mb-3">Email</label>
                        <input type="text" aria-label="Email" pInputText id="email" formControlName="email"
                            placeholder="Email" autofocus fluid />
                        <app-form-error [control]="account_opener.controls['email']" [controlName]="'Email'"
                            [apiErrorType]="'email'" />
                    </div>
                    <div>
                        <label for="title" class="required-label block font-bold mb-3">Title</label>
                        <input type="text" aria-label="Title" pInputText id="title" formControlName="title"
                            placeholder="Title" autofocus fluid />
                        <app-form-error [control]="account_opener.controls['title']" [controlName]="'Title'"
                            [apiErrorType]="'title'" />
                    </div>
                    <div>
                        <label for="id_number" class="required-label block font-bold mb-3">ID Number</label>
                        <input type="text" aria-label="ID Number" pInputText id="id_number" formControlName="id_number"
                            placeholder="ID Number" autofocus fluid />
                        <app-form-error [control]="account_opener.controls['id_number']" [controlName]="'ID Number'"
                            [apiErrorType]="'id_number'" />
                    </div>
                    <div>
                        <label for="phone_number" class="required-label block font-bold mb-3">Phone Number</label>
                        <input type="text" aria-label="Phone Number" pInputText id="phone_number"
                            formControlName="phone_number" placeholder="Phone Number" autofocus fluid />
                        <app-form-error [control]="account_opener.controls['phone_number']"
                            [controlName]="'Phone Number'" [apiErrorType]="'phone_number'" />
                    </div>
                    <div>
                        <label for="dob" class="required-label block font-bold mb-3">Date of Birth</label>
                        <p-datePicker formControlName="dob" [showIcon]="true" [appendTo]="'body'" [required]="true"
                            placeholder="dd-mm-yyyy" styleClass="w-full" [maxDate]="maxDate" [defaultDate]="maxDate"
                            dateFormat="dd-mm-yy" />
                        <app-form-error [control]="account_opener.controls['dob']" [controlName]="'Date of Birth'"
                            [apiErrorType]="'dob'" />
                    </div>
                </div>
            </div>
            <div class="mt-4">
                <div class="text-lg mb-2">Account Opener Address Details</div>
            </div>
            <div [formGroup]="account_opener">
                <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 mb-4">
                    <div>
                        <label for="line1" class="required-label block font-bold mb-3">Address Line 1</label>
                        <input type="text" aria-label="Address Line 1" pInputText id="line1" formControlName="line1"
                            placeholder="Address Line 1" autofocus fluid />
                        <app-form-error [control]="account_opener.controls['line1']" [controlName]="'Address Line 1'"
                            [apiErrorType]="'line1'" />
                    </div>
                    <div>
                        <label for="line2" class="block font-bold mb-3">Address Line 2</label>
                        <input type="text" aria-label="Address Line 2" pInputText id="line2" formControlName="line2"
                            placeholder="Address Line 2" autofocus fluid />
                        <app-form-error [control]="account_opener.controls['line2']" [controlName]="'Address Line 2'"
                            [apiErrorType]="'line2'" />
                    </div>
                    <div>
                        <label for="city" class="required-label block font-bold mb-3">City</label>
                        <input type="text" aria-label="City" pInputText id="city" formControlName="city"
                            placeholder="City" autofocus fluid />
                        <app-form-error [control]="account_opener.controls['city']" [controlName]="'City'"
                            [apiErrorType]="'city'" />
                    </div>
                    <div>
                        <label for="state" class="required-label block font-bold mb-3">State</label>
                        <input type="text" aria-label="State" pInputText id="state" formControlName="state"
                            placeholder="State" autofocus fluid />
                        <app-form-error [control]="account_opener.controls['state']" [controlName]="'State'"
                            [apiErrorType]="'state'" />
                    </div>
                    <div>
                        <label for="postal_code" class="required-label block font-bold mb-3">Postal Code</label>
                        <input type="text" aria-label="Postal Code" pInputText id="postal_code"
                            formControlName="postal_code" placeholder="Postal Code" autofocus fluid />
                        <app-form-error [control]="account_opener.controls['postal_code']" [controlName]="'Postal Code'"
                            [apiErrorType]="'postal_code'" />
                    </div>
                </div>
            </div>
            <div class="mt-4">
                <div class="text-lg mb-2">Account Opener ID Proofs</div>
            </div>
            <div [formGroup]="connectAccountForm">
                <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 mb-4">
                    <div>
                        <label for="file_account_opener_doc_front" class="required-label block font-bold mb-3">Front
                            photo of ID</label>
                        <input type="file" aria-label="ID Proof" pInputText id="file_account_opener_doc_front"
                            (change)="onFileSelected($event, 'file_account_opener_doc_front')" placeholder="ID Proof"
                            autofocus fluid />
                        <app-form-error [control]="connectAccountForm.controls['file_account_opener_doc_front']"
                            [controlName]="'Front photo of ID'" [apiErrorType]="'file_account_opener_doc_front'" />
                    </div>
                    <div>
                        <label for="file_account_opener_doc_back" class="required-label block font-bold mb-3">Back
                            photo of ID</label>
                        <input type="file" aria-label="ID Proof" pInputText id="file_account_opener_doc_back"
                            (change)="onFileSelected($event, 'file_account_opener_doc_back')" placeholder="ID Proof"
                            autofocus fluid />
                        <app-form-error [control]="connectAccountForm.controls['file_account_opener_doc_back']"
                            [controlName]="'Back photo of ID'" [apiErrorType]="'file_account_opener_doc_back'" />
                    </div>
                    <div>
                        <label for="file_account_opener_add_front" class="required-label block font-bold mb-3">Addional
                            photo of
                            ID</label>
                        <input type="file" aria-label="ID Proof" pInputText id="file_account_opener_add_front"
                            (change)="onFileSelected($event, 'file_account_opener_add_front')" placeholder="ID Proof"
                            autofocus fluid />
                        <app-form-error [control]="connectAccountForm.controls['file_account_opener_add_front']"
                            [controlName]="'Addional photo of ID'" [apiErrorType]="'file_account_opener_add_front'" />
                    </div>
                </div>
            </div>
            <ng-container *ngIf="connectAccountForm.controls['business_type'].value === 'company'">
                <div class="mt-4">
                    <div class="text-lg mb-2">Account Owner Details</div>
                </div>
                <div [formGroup]="account_owner">
                    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 mb-4">
                        <div>
                            <label for="first_name" class="required-label block font-bold mb-3">First Name</label>
                            <input type="text" aria-label="First Name" pInputText id="first_name"
                                formControlName="first_name" placeholder="First Name" autofocus fluid />
                            <app-form-error [control]="account_owner.controls['first_name']"
                                [controlName]="'First Name'" [apiErrorType]="'first_name'" />
                        </div>
                        <div>
                            <label for="last_name" class="required-label block font-bold mb-3">Last Name</label>
                            <input type="text" aria-label="Last Name" pInputText id="last_name"
                                formControlName="last_name" placeholder="Last Name" autofocus fluid />
                            <app-form-error [control]="account_owner.controls['last_name']" [controlName]="'Last Name'"
                                [apiErrorType]="'last_name'" />
                        </div>
                        <div>
                            <label for="email" class="required-label block font-bold mb-3">Email</label>
                            <input type="text" aria-label="Email" pInputText id="email" formControlName="email"
                                placeholder="Email" autofocus fluid />
                            <app-form-error [control]="account_owner.controls['email']" [controlName]="'Email'"
                                [apiErrorType]="'email'" />
                        </div>
                        <div>
                            <label for="title" class="required-label block font-bold mb-3">Title</label>
                            <input type="text" aria-label="Title" pInputText id="title" formControlName="title"
                                placeholder="Title" autofocus fluid />
                            <app-form-error [control]="account_owner.controls['title']" [controlName]="'Title'"
                                [apiErrorType]="'title'" />
                        </div>
                        <div>
                            <label for="id_number" class="required-label block font-bold mb-3">ID Number</label>
                            <input type="text" aria-label="ID Number" pInputText id="id_number"
                                formControlName="id_number" placeholder="ID Number" autofocus fluid />
                            <app-form-error [control]="account_owner.controls['id_number']" [controlName]="'ID Number'"
                                [apiErrorType]="'id_number'" />
                        </div>
                        <div>
                            <label for="phone_number" class="required-label block font-bold mb-3">Phone Number</label>
                            <input type="text" aria-label="Phone Number" pInputText id="phone_number"
                                formControlName="phone_number" placeholder="Phone Number" autofocus fluid />
                            <app-form-error [control]="account_owner.controls['phone_number']"
                                [controlName]="'Phone Number'" [apiErrorType]="'phone_number'" />
                        </div>
                        <div>
                            <label for="dob" class="required-label block font-bold mb-3">Date of Birth</label>
                            <p-datePicker formControlName="dob" [showIcon]="true" [appendTo]="'body'"
                                styleClass="w-full" dateFormat="dd-mm-yy" placeholder="dd-mm-yyyy" [maxDate]="maxDate"
                                [defaultDate]="maxDate" />
                            <app-form-error [control]="account_owner.controls['dob']" [controlName]="'Date of Birth'"
                                [apiErrorType]="'dob'" />
                        </div>
                        <div>
                            <label for="percent_ownership" class="required-label block font-bold mb-3">Percent
                                Ownership</label>
                            <input type="number" aria-label="Percent Ownership" pInputText id="percent_ownership"
                                formControlName="percent_ownership" placeholder="Percent Ownership" autofocus fluid />
                            <app-form-error [control]="account_owner.controls['percent_ownership']"
                                [controlName]="'Percent Ownership'" [apiErrorType]="'percent_ownership'" />
                        </div>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="text-lg mb-2">Account Owner Address Details</div>
                </div>
                <div [formGroup]="account_owner">
                    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 mb-4">
                        <div>
                            <label for="line1" class="required-label block font-bold mb-3">Address Line 1</label>
                            <input type="text" aria-label="Address Line 1" pInputText id="line1" formControlName="line1"
                                placeholder="Address Line 1" autofocus fluid />
                            <app-form-error [control]="account_owner.controls['line1']" [controlName]="'Address Line 1'"
                                [apiErrorType]="'line1'" />
                        </div>
                        <div>
                            <label for="line2" class="block font-bold mb-3">Address Line 2</label>
                            <input type="text" aria-label="Address Line 2" pInputText id="line2" formControlName="line2"
                                placeholder="Address Line 2" autofocus fluid />
                            <app-form-error [control]="account_owner.controls['line2']" [controlName]="'Address Line 2'"
                                [apiErrorType]="'line2'" />
                        </div>
                        <div>
                            <label for="city" class="required-label block font-bold mb-3">City</label>
                            <input type="text" aria-label="City" pInputText id="city" formControlName="city"
                                placeholder="City" autofocus fluid />
                            <app-form-error [control]="account_owner.controls['city']" [controlName]="'City'"
                                [apiErrorType]="'city'" />
                        </div>
                        <div>
                            <label for="state" class="required-label block font-bold mb-3">State</label>
                            <input type="text" aria-label="State" pInputText id="state" formControlName="state"
                                placeholder="State" autofocus fluid />
                            <app-form-error [control]="account_owner.controls['state']" [controlName]="'State'"
                                [apiErrorType]="'state'" />
                        </div>
                        <div>
                            <label for="postal_code" class="required-label block font-bold mb-3">Postal Code</label>
                            <input type="text" aria-label="Postal Code" pInputText id="postal_code"
                                formControlName="postal_code" placeholder="Postal Code" autofocus fluid />
                            <app-form-error [control]="account_owner.controls['postal_code']"
                                [controlName]="'Postal Code'" [apiErrorType]="'postal_code'" />
                        </div>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="text-lg mb-2">Account Owner ID Proofs</div>
                </div>
                <div [formGroup]="connectAccountForm">
                    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 mb-4">
                        <div>
                            <label for="file_account_owner_doc_front" class="required-label block font-bold mb-3">Front
                                photo of ID</label>
                            <input type="file" aria-label="ID Proof" pInputText id="file_account_owner_doc_front"
                                (change)="onFileSelected($event, 'file_account_owner_doc_front')" placeholder="ID Proof"
                                autofocus fluid />
                            <app-form-error [control]="connectAccountForm.controls['file_account_owner_doc_front']"
                                [controlName]="'Front photo of ID'" [apiErrorType]="'file_account_owner_doc_front'" />
                        </div>
                        <div>
                            <label for="file_account_owner_doc_back" class="required-label block font-bold mb-3">Back
                                photo of ID</label>
                            <input type="file" aria-label="ID Proof" pInputText id="file_account_owner_doc_back"
                                (change)="onFileSelected($event, 'file_account_owner_doc_back')" placeholder="ID Proof"
                                autofocus fluid />
                            <app-form-error [control]="connectAccountForm.controls['file_account_owner_doc_back']"
                                [controlName]="'Back photo of ID'" [apiErrorType]="'file_account_owner_doc_back'" />
                        </div>
                        <div>
                            <label for="file_account_owner_add_front"
                                class="required-label block font-bold mb-3">Addional photo of
                                ID</label>
                            <input type="file" aria-label="ID Proof" pInputText id="file_account_owner_add_front"
                                (change)="onFileSelected($event, 'file_account_owner_add_front')" placeholder="ID Proof"
                                autofocus fluid />
                            <app-form-error [control]="connectAccountForm.controls['file_account_owner_add_front']"
                                [controlName]="'Addional photo of ID'"
                                [apiErrorType]="'file_account_owner_add_front'" />
                        </div>
                    </div>
                </div>
            </ng-container>
            <div class="mt-4">
                <div class="text-lg mb-2">Bank Details</div>
            </div>
            <div [formGroup]="external_account">
                <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 mb-4">
                    <div>
                        <label for="account_holder_name" class="required-label block font-bold mb-3">Account Holder
                            Name</label>
                        <input type="text" aria-label="Account Holder Name" pInputText id="account_holder_name"
                            formControlName="account_holder_name" placeholder="Account Holder Name" autofocus fluid />
                        <app-form-error [control]="external_account.controls['account_holder_name']"
                            [controlName]="'Account Holder Name'" [apiErrorType]="'account_holder_name'" />
                    </div>
                    <div>
                        <label for="account_holder_type" class="required-label block font-bold mb-3">Account Holder
                            Type</label>
                        <p-select [options]="business_types" optionLabel="name" optionValue="id" [showClear]="true"
                            formControlName="account_holder_type" placeholder="Select a account holder type"
                            [appendTo]="'body'" fluid />
                        <app-form-error [control]="external_account.controls['account_holder_type']"
                            [controlName]="'Account Holder Type'" [apiErrorType]="'account_holder_type'" />
                    </div>
                    <div>
                        <label for="country" class="required-label block font-bold mb-3">Country</label>
                        <p-select [options]="countries" optionLabel="name" optionValue="iso" [showClear]="true"
                            formControlName="country" placeholder="Select a country" [appendTo]="'body'" required
                            fluid />
                        <app-form-error [control]="external_account.controls['country']" [controlName]="'Country'"
                            [apiErrorType]="'country'" />
                    </div>
                    <div>
                        <label for="account_number" class="required-label block font-bold mb-3">Account Number</label>
                        <input type="text" aria-label="Account Number" pInputText id="account_number"
                            formControlName="account_number" placeholder="Account Number" autofocus fluid />
                        <app-form-error [control]="external_account.controls['account_number']"
                            [controlName]="'Account Number'" [apiErrorType]="'account_number'" />
                    </div>
                    <div>
                        <label for="routing_number" class="required-label block font-bold mb-3">Sort Code</label>
                        <input type="text" aria-label="Sort Code" pInputText id="routing_number"
                            formControlName="routing_number" placeholder="Sort Code" autofocus fluid />
                        <app-form-error [control]="external_account.controls['routing_number']"
                            [controlName]="'Sort Code'" [apiErrorType]="'routing_number'" />
                    </div>
                </div>
            </div>
        </form>
    </ng-template>
    <ng-template #footer>
        <div class="flex items-center gap-2">
            <button pButton label="Cancel" icon="pi pi-times" class="w-full" outlined (click)="closeDialog()"></button>
            <button pButton label="Save" icon="pi pi-check" class="w-full" (click)="saveConnectAccount()"></button>
        </div>
    </ng-template>
</p-drawer>