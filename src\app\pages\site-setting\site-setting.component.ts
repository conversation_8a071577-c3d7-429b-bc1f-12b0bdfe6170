import { Component, inject, OnInit, ViewChild } from '@angular/core';
import { PrimengModules } from '../../core/utils/primeng';
import { AuthService } from '../../core/services/authentication.service';
import { ApiService } from '../../core/services/api.service';
import { MessageService } from 'primeng/api';
import { LoaderService } from '../../core/services/loader.service';
import { filterParams } from '../../core/utils/utils';
import { HttpParams } from '@angular/common/http';
import { Table } from 'primeng/table';

@Component({
  selector: 'app-site-setting',
  imports: [PrimengModules],
  standalone: true,
  templateUrl: './site-setting.component.html',
  styleUrl: './site-setting.component.scss'
})
export class SiteSettingComponent implements OnInit {
  public authService = inject(AuthService);
  private apiService = inject(ApiService);
  private loaderService = inject(LoaderService);
  private messageService = inject(MessageService);

  @ViewChild('dt') dt!: Table;

  siteSettings: any = null;
  clonedsiteSettings: { [s: string]: any } = {};

  constructor() { }

  ngOnInit() { this.fetchSiteSettings(); }

  fetchSiteSettings() {
    const httpParams: HttpParams = filterParams({ nopaginate: 1 });
    this.loaderService.show();
    this.apiService.get('site-settings', httpParams).subscribe({
      next: (res: any) => { this.siteSettings = res; },
      error: (err: any) => { this.loaderService.hide(); },
      complete: () => { this.loaderService.hide(); },
    });
  }

  onGlobalFilter(table: Table, event: Event) {
    table.filterGlobal((event.target as HTMLInputElement).value, 'contains');
  }

  fetchLoadSiteSettings() {
    this.loaderService.show();
    this.apiService.post('site-settings/load-settings', {}).subscribe({
      next: (res: any) => { this.fetchSiteSettings(); },
      error: (err: any) => { this.loaderService.hide(); },
      complete: () => { this.loaderService.hide(); },
    });
  }

  onRowEditInit(siteSetting: any) {
    this.clonedsiteSettings[siteSetting.id] = { ...siteSetting };
  }

  onRowEditSave(siteSetting: any) {
    if (siteSetting) {
      delete this.clonedsiteSettings[siteSetting.id as string];
      this.loaderService.show();
      this.apiService.post(`site-settings/${siteSetting.id}`, siteSetting).subscribe({
        next: (res: any) => {
          this.fetchSiteSettings();
          this.messageService.add({ severity: 'success', summary: 'Success', detail: 'Site Setting is updated' });
        },
        error: (err: any) => {
          this.loaderService.hide();
          this.fetchSiteSettings();
          this.messageService.add({ severity: 'error', summary: 'Error', detail: err.error.message });
        },
        complete: () => { this.loaderService.hide(); },
      });
    }
  }

  onRowEditCancel(siteSetting: any, index: number) {
    this.siteSettings[index] = this.clonedsiteSettings[siteSetting.id as string];
    delete this.clonedsiteSettings[siteSetting.id as string];
  }

}

