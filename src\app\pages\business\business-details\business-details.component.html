<p-panel header="Basic Details" [toggleable]="true" styleClass="mt-4">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
        <dl class="divide-y divide-gray-200">
            <div class="py-3 flex items-center border-b border-gray-200">
                <i class="fas fa-building text-primary-500 mr-3 w-6 h-6 flex items-center justify-center"></i>
                <dt class="font-bold w-22 sm:w-42 md:w-32 lg:w-52">Business Name</dt>
                <dt class="font-bold w-6 hidden sm:block">:</dt>
                <dd class="flex-1 break-all">{{ business?.name || '--' }}</dd>
            </div>
        </dl>
        <dl class="divide-y divide-gray-200">
            <div class="py-3 flex items-center border-b border-gray-200">
                <i class="fas fa-signature text-primary-500 mr-3 w-6 h-6 flex items-center justify-center"></i>
                <dt class="font-bold w-22 sm:w-42 md:w-32 lg:w-52">Trading Name</dt>
                <dt class="font-bold w-6 hidden sm:block">:</dt>
                <dd class="flex-1 break-all">{{ business?.trading_name || '--' }}</dd>
            </div>
        </dl>
        <dl class="divide-y divide-gray-200">
            <div class="py-3 flex items-center border-b border-gray-200">
                <i class="fas fa-envelope text-primary-500 mr-3 w-6 h-6 flex items-center justify-center"></i>
                <dt class="font-bold w-22 sm:w-42 md:w-32 lg:w-52">Business Email</dt>
                <dt class="font-bold w-6 hidden sm:block">:</dt>
                <dd class="flex-1 break-all">{{ business?.email || '--' }}</dd>
            </div>
        </dl>
        <dl class="divide-y divide-gray-200">
            <div class="py-3 flex items-center border-b border-gray-200">
                <i class="fas fa-phone text-primary-500 mr-3 w-6 h-6 flex items-center justify-center"></i>
                <dt class="font-bold w-22 sm:w-42 md:w-32 lg:w-52">Contact Numbers</dt>
                <dt class="font-bold w-6 hidden sm:block">:</dt>
                <dd class="flex-1 break-all">{{ business?.contact_numbers || '--' }}</dd>
            </div>
        </dl>
        <dl class="divide-y divide-gray-200">
            <div class="py-3 flex items-center border-b border-gray-200">
                <i class="fas fa-address-card text-primary-500 mr-3 w-6 h-6 flex items-center justify-center"></i>
                <dt class="font-bold w-22 sm:w-42 md:w-32 lg:w-52">Business Address</dt>
                <dt class="font-bold w-6 hidden sm:block">:</dt>
                <dd class="flex-1 break-all">{{ business?.address || '--' }}</dd>
            </div>
        </dl>
        <dl class="divide-y divide-gray-200">
            <div class="py-3 flex items-center border-b border-gray-200">
                <i class="fas fa-city text-primary-500 mr-3 w-6 h-6 flex items-center justify-center"></i>
                <dt class="font-bold w-22 sm:w-42 md:w-32 lg:w-52">City</dt>
                <dt class="font-bold w-6 hidden sm:block">:</dt>
                <dd class="flex-1 break-all">{{ business?.city || '--' }}</dd>
            </div>
        </dl>
        <dl class="divide-y divide-gray-200">
            <div class="py-3 flex items-center border-b border-gray-200">
                <i class="fas fa-map-marker-alt text-primary-500 mr-3 w-6 h-6 flex items-center justify-center"></i>
                <dt class="font-bold w-22 sm:w-42 md:w-32 lg:w-52">State</dt>
                <dt class="font-bold w-6 hidden sm:block">:</dt>
                <dd class="flex-1 break-all">{{ business?.state || '--' }}</dd>
            </div>
        </dl>
        <dl class="divide-y divide-gray-200">
            <div class="py-3 flex items-center border-b border-gray-200">
                <i class="fas fa-globe text-primary-500 mr-3 w-6 h-6 flex items-center justify-center"></i>
                <dt class="font-bold w-22 sm:w-42 md:w-32 lg:w-52">Country</dt>
                <dt class="font-bold w-6 hidden sm:block">:</dt>
                <dd class="flex-1 break-all">{{ business?.country?.name || '--' }}</dd>
            </div>
        </dl>
        <dl class="divide-y divide-gray-200">
            <div class="py-3 flex items-center border-b border-gray-200">
                <i class="fas fa-map-marker-alt text-primary-500 mr-3 w-6 h-6 flex items-center justify-center"></i>
                <dt class="font-bold w-22 sm:w-42 md:w-32 lg:w-52">Postcode</dt>
                <dt class="font-bold w-6 hidden sm:block">:</dt>
                <dd class="flex-1 break-all">{{ business?.postcode || '--' }}</dd>
            </div>
        </dl>
        <dl class="divide-y divide-gray-200">
            <div class="py-3 flex items-center border-b border-gray-200">
                <i class="fas fa-door-open text-primary-500 mr-3 w-6 h-6 flex items-center justify-center"></i>
                <dt class="font-bold w-22 sm:w-42 md:w-32 lg:w-52">Business Door No</dt>
                <dt class="font-bold w-6 hidden sm:block">:</dt>
                <dd class="flex-1 break-all">{{ business?.door_no || '--' }}</dd>
            </div>
        </dl>
        <dl class="divide-y divide-gray-200">
            <div class="py-3 flex items-center border-b border-gray-200">
                <i class="fas fa-link text-primary-500 mr-3 w-6 h-6 flex items-center justify-center"></i>
                <dt class="font-bold w-22 sm:w-42 md:w-32 lg:w-52">Business URL</dt>
                <dt class="font-bold w-6 hidden sm:block">:</dt>
                <dd class="flex-1 break-all">{{ business?.domain || '--' }}</dd>
            </div>
        </dl>
    </div>

</p-panel>

@if(business?.company_number){
<p-panel header="Company Details" [toggleable]="true" styleClass="mt-4">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
        <!-- Company Number -->
        <dl class="divide-y divide-gray-200">
            <div class="py-3 flex items-center border-b border-gray-200">
                <i class="fas fa-id-card-clip text-primary-500 mr-3 w-6 h-6 flex items-center justify-center"></i>
                <dt class="font-bold w-22 sm:w-42 md:w-32 lg:w-52">Company Number</dt>
                <dt class="font-bold w-6 hidden sm:block">:</dt>
                <dd class="flex-1 break-all">{{ business?.company_number || '--' }}</dd>
            </div>
        </dl>
        <!-- Company Name -->
        <dl class="divide-y divide-gray-200">
            <div class="py-3 flex items-center border-b border-gray-200">
                <i class="fas fa-signature text-primary-500 mr-3 w-6 h-6 flex items-center justify-center"></i>
                <dt class="font-bold w-22 sm:w-42 md:w-32 lg:w-52">Company Name</dt>
                <dt class="font-bold w-6 hidden sm:block">:</dt>
                <dd class="flex-1 break-all">{{ business?.company_name || '--' }}</dd>
            </div>
        </dl>
        <!-- Company Type -->
        <dl class="divide-y divide-gray-200">
            <div class="py-3 flex items-center border-b border-gray-200"> <i
                    class="fas fa-building text-primary-500 mr-3 w-6 h-6 flex items-center justify-center"></i>
                <dt class="font-bold w-22 sm:w-42 md:w-32 lg:w-52">Company Type</dt>
                <dt class="font-bold w-6 hidden sm:block">:</dt>
                <dd class="flex-1 break-all">{{ business?.company_type || '--' }}</dd>
            </div>
        </dl>
        <!-- Company Address Line 1 -->
        <dl class="divide-y divide-gray-200">
            <div class="py-3 flex items-center border-b border-gray-200">
                <i class="fas fa-map-marker-alt text-primary-500 mr-3 w-6 h-6 flex items-center justify-center"></i>
                <dt class="font-bold w-22 sm:w-42 md:w-32 lg:w-52">Company Address Line 1</dt>
                <dt class="font-bold w-6 hidden sm:block">:</dt>
                <dd class="flex-1 break-all">{{ business?.company_address_line_1 || '--' }}</dd>
            </div>
        </dl>
        <!-- Company Address Line 2 -->
        <dl class="divide-y divide-gray-200">
            <div class="py-3 flex items-center border-b border-gray-200"> <i
                    class="fas fa-map-marker-alt text-primary-500 mr-3 w-6 h-6 flex items-center justify-center"></i>
                <dt class="font-bold w-22 sm:w-42 md:w-32 lg:w-52">Company Address Line 2</dt>
                <dt class="font-bold w-6 hidden sm:block">:</dt>
                <dd class="flex-1 break-all">{{ business?.company_address_line_2 || '--' }}</dd>
            </div>
        </dl>
        <!-- Company Locality -->
        <dl class="divide-y divide-gray-200">
            <div class="py-3 flex items-center border-b border-gray-200">
                <i class="fas fa-map-marker-alt text-primary-500 mr-3 w-6 h-6 flex items-center justify-center"></i>
                <dt class="font-bold w-22 sm:w-42 md:w-32 lg:w-52">Company Locality</dt>
                <dt class="font-bold w-6 hidden sm:block">:</dt>
                <dd class="flex-1 break-all">{{ business?.company_locality || '--' }}</dd>
            </div>
        </dl>
        <!-- Company Postal Code -->
        <dl class="divide-y divide-gray-200">
            <div class="py-3 flex items-center border-b border-gray-200">
                <i class="fas fa-map-marker-alt text-primary-500 mr-3 w-6 h-6 flex items-center justify-center"></i>
                <dt class="font-bold w-22 sm:w-42 md:w-32 lg:w-52">Company Postal Code</dt>
                <dt class="font-bold w-6 hidden sm:block">:</dt>
                <dd class="flex-1 break-all">{{ business?.company_postal_code || '--' }}</dd>
            </div>
        </dl>
        <!-- Company Country -->
        <dl class="divide-y divide-gray-200">
            <div class="py-3 flex items-center border-b border-gray-200">
                <i class="fas fa-globe text-primary-500 mr-3 w-6 h-6 flex items-center justify-center"></i>
                <dt class="font-bold w-22 sm:w-42 md:w-32 lg:w-52">Company Country</dt>
                <dt class="font-bold w-6 hidden sm:block">:</dt>
                <dd class="flex-1 break-all">{{ business?.company_country || '--' }}</dd>
            </div>
        </dl>
        <!-- Company Region -->
        <dl class="divide-y divide-gray-200">
            <div class="py-3 flex items-center border-b border-gray-200">
                <i class="fas fa-map-marker-alt text-primary-500 mr-3 w-6 h-6 flex items-center justify-center"></i>
                <dt class="font-bold w-22 sm:w-42 md:w-32 lg:w-52">Company Region</dt>
                <dt class="font-bold w-6 hidden sm:block">:</dt>
                <dd class="flex-1 break-all">{{ business?.company_region || '--' }}</dd>
            </div>
        </dl>
        <!-- Company Date of Creation -->
        <dl class="divide-y divide-gray-200">
            <div class="py-3 flex items-center border-b border-gray-200">
                <i class="fas fa-calendar text-primary-500 mr-3 w-6 h-6 flex items-center justify-center"></i>
                <dt class="font-bold w-22 sm:w-42 md:w-32 lg:w-52">Company Date of Creation</dt>
                <dt class="font-bold w-6 hidden sm:block">:</dt>
                <dd class="flex-1 break-all">{{ business?.company_date_of_creation || '--' }}</dd>
            </div>
        </dl>
        <!-- Company Status -->
        <dl class="divide-y divide-gray-200">
            <div class="py-3 flex items-center border-b border-gray-200"> <i
                    class="fas fa-check text-primary-500 mr-3 w-6 h-6 flex items-center justify-center"></i>
                <dt class="font-bold w-22 sm:w-42 md:w-32 lg:w-52">Company Status</dt>
                <dt class="font-bold w-6 hidden sm:block">:</dt>
                <dd class="flex-1 break-all">{{ business?.company_status | titlecase }}</dd>
            </div>
        </dl>
    </div>
</p-panel>

<p-panel header="Company People Details" [toggleable]="true" styleClass="mt-4">
    <!-- Fetch Company People Details button -->
    <div class="flex justify-end mb-4">
        <p-button label="Fetch Company People Details" (click)="fetchPeopleLookup()" />
    </div>
    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2">
        <div *ngFor="let person of business?.company_peoples">
            <p-card styleClass="w-full">
                <ng-template #title> {{ person.name }} </ng-template>
                <ng-template #subtitle> {{ person.officer_role | titlecase }} </ng-template>
                <div class="border-t border-gray-200 pt-4">
                    <div class="mb-2">
                        <strong>Appointed On:</strong>
                        {{ person.appointed_on ? (person.appointed_on | date: 'dd-MM-yyyy') : 'N/A' }}
                    </div>

                    <div class="mb-2" *ngIf="person.resigned_on">
                        <strong>Resigned On:</strong> {{ person.resigned_on | date: 'dd-MM-yyyy' }}
                    </div>

                    <div class="mb-2"><strong>Occupation:</strong> {{ person.occupation || 'N/A' }}</div>
                    <div class="mb-2"><strong>Nationality:</strong> {{ person.nationality || 'N/A' }}</div>
                    <div class="mb-2"><strong>Country:</strong> {{ person.country_of_residence || 'N/A' }}</div>

                    <div class="mb-2"><strong>Date of Birth:</strong> {{ person.date_of_birth || 'N/A' }}</div>

                    <div class="mb-2">
                        <strong>Address:</strong>
                        <div>
                            {{ person.address_line_1 || '' }}{{ person.address_line_2 ? ', ' + person.address_line_2
                            : '' }}<br>
                            {{ person.locality || '' }}{{ person.postal_code ? ', ' + person.postal_code : '' }}{{
                            person.country ? ', ' + person.country : '' }}
                        </div>
                    </div>
                </div>
            </p-card>
        </div>
    </div>
</p-panel>

}
