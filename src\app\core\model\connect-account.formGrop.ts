import { AbstractControl, FormBuilder, FormGroup, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';
import { checkBothPasswords, minimumAgeValidator, urlValidator } from '../utils/utils';


export function createConnectAccountForm(fb: FormBuilder): FormGroup {
  return fb.group({
    id: [''],
    s_account_id: [''],
    business_id: ['', Validators.required],
    display_name: ['', Validators.required],
    business_type: ["individual", Validators.required],
    tos_ip: [''],
    tos_date: [''],
    country: ['', Validators.required],
    schedule_delay_days: [7],
    schedule_interval: [''],
    schedule_weekly_anchor: [''],
    schedule_monthly_anchor: [''],
    file_account_opener_doc_front: ['', Validators.required],
    file_account_opener_doc_back: ['', Validators.required],
    file_account_opener_add_front: [null],
    file_account_owner_doc_front: [null],
    file_account_owner_doc_back: [null],
    file_account_owner_add_front: [null],
  });
}


export function createCompanyGroup(fb: FormBuilder): FormGroup {
  return fb.group({
    connect_account_id: [''],
    s_account_id: [''],
    structure: [''],
    name: ['', Validators.required],
    email: ['', [Validators.email, Validators.required]],
    url: ['', [Validators.required, urlValidator]],
    mcc: ['5814', Validators.required],
    annual_revenue_amount: [null, Validators.required],
    fiscal_year_end: ['', Validators.required],
    estimated_worker_count: ['', Validators.required],
    tax_id: ['', Validators.required],
    phone_number: ['', Validators.required],
    line1: ['', Validators.required],
    line2: [''],
    city: ['', Validators.required],
    state: ['', Validators.required],
    postal_code: ['', Validators.required],
  });
}


export function createExternalAccountGroup(fb: FormBuilder): FormGroup {
  return fb.group({
    connect_account_id: [''],
    s_account_id: [''],
    business_id: [''],
    s_bank_account_id: [''],
    country: ['', Validators.required],
    currency: [''],
    account_holder_name: ['', Validators.required],
    account_holder_type: ['individual', Validators.required],
    account_number: ['', Validators.required],
    routing_number: ['', Validators.required],
    description: ['FMS'],
    statement_descriptor: [''],
    default: [false],
    is_customer: [false],
  });
}

export function createPersonGroup(fb: FormBuilder): FormGroup {
  return fb.group({
    connect_account_id: [''],
    company_id: [''],
    s_account_id: [''],
    s_person_id: [''],
    first_name: ['', Validators.required],
    last_name: ['', Validators.required],
    email: ['', [Validators.email]],
    title: ['', Validators.required],
    id_number: ['', Validators.required],
    phone_number: ['', Validators.required],
    dob: ['', [Validators.required, minimumAgeValidator(18)]],
    line1: ['', Validators.required],
    line2: [''],
    city: ['', Validators.required],
    state: ['', Validators.required],
    postal_code: ['', Validators.required],
    owner: [false],
    percent_ownership: [''],
    director: [false],
    executive: [false],
    account_opener: [false],
    representative: [false],
  });
}


