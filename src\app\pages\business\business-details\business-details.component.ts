import { Component, EventEmitter, inject, Input, OnInit, Output } from '@angular/core';
import { ButtonModule } from 'primeng/button';
import { PanelModule } from 'primeng/panel';
import { PrimengModules } from '../../../core/utils/primeng';
import { ApiService } from '../../../core/services/api.service';
import { LoaderService } from '../../../core/services/loader.service';
import { MessageService } from 'primeng/api';

@Component({
  selector: 'app-business-details',
  imports: [PrimengModules],
  standalone: true,
  templateUrl: './business-details.component.html',
  styleUrl: './business-details.component.scss',
  providers: [],
})
export class BusinessDetailsComponent {
  private apiService = inject(ApiService);
  private loaderService = inject(LoaderService);
  private messageService = inject(MessageService);

  @Input() business: any = null;
  @Output() save = new EventEmitter<any>();

  fetchPeopleLookup() {
    this.loaderService.show();
    this.apiService.get(`businesses/${this.business.id}/people-lookup`).subscribe({
      next: (res: any) => { this.save.emit(res); },
      error: (err: any) => { this.loaderService.hide();
        this.messageService.add({ severity: 'error', summary: 'Error', detail: err.error.message, life: 3000, });
       },
      complete: () => { this.loaderService.hide(); },
    });
  }
}
