import { Component, EventEmitter, inject, Input, OnChanges, OnInit, Output } from '@angular/core';
import { PrimengModules } from '../../../core/utils/primeng';
import { FormBuilder, FormGroup } from '@angular/forms';
import { createPermissionGroup } from '../../../core/utils/permissionsForm';

@Component({
  selector: 'app-permissions',
  imports: [PrimengModules],
  standalone: true,
  templateUrl: './permissions.component.html',
  styleUrl: './permissions.component.scss'
})
export class PermissionsComponent implements OnInit, OnChanges {
  private fb = inject(FormBuilder);

  @Input() permissions: any = null;
  @Output() permissionsChange = new EventEmitter<any>();

  permissionsForm!: FormGroup;
  searchTerm: string = '';

  constructor() {
    this.permissionsForm = createPermissionGroup(this.fb);
  }

  ngOnInit() {
    this.permissionsForm.valueChanges.subscribe((val) => {
      this.permissionsChange.emit(val);
    });
  }

  ngOnChanges() {
    if (this.permissions) {
      this.permissionsForm.patchValue(this.permissions);
      this.permissionsForm.updateValueAndValidity();
    }
  }

}
