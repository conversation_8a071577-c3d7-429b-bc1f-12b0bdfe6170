/* You can add global styles to this file, and also import other style files */

@use "./tailwind.css";
@use "primeicons/primeicons.css";
@use "./assets/demo/demo.scss";
@use "./assets/layout/layout.scss";

/* global stylesheet */
.required-label::after {
  content: "*";
  color: #ef4444; /* tailwind red-500 value */
  margin-left: 0.25rem;
  font-weight: 600;
}

/* Remove text cursor & text selection globally */
* {
  // user-select: none; /* Prevents text highlight */
  // -webkit-user-select: none;
  // -moz-user-select: none;
  // -ms-user-select: none;
}

body {
  cursor: default; /* Default arrow cursor */
  font-family: "Nunito Sans", sans-serif;
}

html {
  scrollbar-width: thin;
  font-family: "Nunito Sans", sans-serif;
}

.p-datatable-scrollable > .p-datatable-table-container {
  scrollbar-width: thin;
}

.p-progressspinner-circle {
  stroke: var(--p-primary-color) !important;
}