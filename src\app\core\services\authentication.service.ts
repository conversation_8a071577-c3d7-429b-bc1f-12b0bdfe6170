import {
  inject,
  Injectable,
} from '@angular/core';
import { ApiService } from './api.service';
import { Inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { BehaviorSubject, catchError, finalize, of, tap } from 'rxjs';
import { LoaderService } from './loader.service';
import { environment } from '../../../environments/environment';
import { Router } from '@angular/router';


@Injectable({
  providedIn: 'root',
})
export class AuthService {
  private loaderService = inject(LoaderService);
  private router = inject(Router);

  _credentials = new BehaviorSubject<any | null>(null);
  credentials$ = this._credentials.asObservable();

  public _permissions: any;

  public _superman: boolean = false;

  constructor(
    private apiService: ApiService,
    @Inject(PLATFORM_ID) private platformId: Object
  ) {
    if (this.hasTokens()) {
      // this.refresh();
    } else {
      this._credentials.next(null);
    }
  }

  refresh() {
    console.log('refreshing');
    this.loaderService.show();
    return this.apiService.get(`auth/me`).pipe(
      tap((res: any) => {
        this.setCredentials(res);
      }),
      catchError((err: any) => {
        this.loaderService.hide();
        this.logOut();
        console.log(`refresh error: `, err);
        return of(null);
      }),
      finalize(() => {
        this.loaderService.hide();
      })
    );
  }

  isAdmin(): boolean {
    console.log(`hasTokens: `, this.hasTokens());
    return this.hasTokens();
  }

  public setCredentials(credentials?: any) {
    if (isPlatformBrowser(this.platformId)) {
      if (credentials) {
        this._credentials.next(credentials);
        this._permissions = credentials.user?.permissions;
        this._superman = credentials.user?.superman;
        if (credentials?.accessToken) {
          localStorage.setItem(environment.accessTokenKey, credentials.accessToken ?? '');
        }
      } else {
        this._credentials.next(null);
      }
    }
  }

  logOut() {
    this._credentials.next(null);
    localStorage.removeItem(environment.accessTokenKey);
    this.router.navigate(['/auth/login']);
  }

  setTokens(tokens: { accessToken: string; refreshToken: string }) {
    localStorage.setItem(environment.accessTokenKey, tokens.accessToken);
  }

  hasTokens(): boolean {
    return (
      !!localStorage.getItem(environment.accessTokenKey)
    );
  }
}
