{"name": "franchise-management-system-admin", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve --host 0.0.0.0 --port 4000", "build": "ng build", "build:prod": "ng build --configuration production --base-href /rootpma/", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/common": "^19.2.0", "@angular/compiler": "^19.2.0", "@angular/core": "^19.2.0", "@angular/forms": "^19.2.0", "@angular/platform-browser": "^19.2.0", "@angular/platform-browser-dynamic": "^19.2.0", "@angular/router": "^19.2.0", "@primeng/themes": "^20.0.1", "@tailwindcss/postcss": "^4.1.10", "chart.js": "^4.5.0", "crypto-js": "^4.2.0", "postcss": "^8.5.6", "primeicons": "^7.0.0", "primeng": "^19.1.4", "rxjs": "~7.8.0", "tailwindcss": "^4.1.10", "tailwindcss-primeui": "^0.6.1", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.9", "@angular/cli": "^19.2.9", "@angular/compiler-cli": "^19.2.0", "@types/crypto-js": "^4.2.2", "@types/jasmine": "~5.1.0", "@types/lodash": "^4.17.20", "jasmine-core": "~5.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.7.2"}}