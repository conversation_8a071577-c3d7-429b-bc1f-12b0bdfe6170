<div class="flex items-center justify-center gap-2 mt-12" *ngIf="!connectAccounts">
    @if(authService._superman || authService._permissions?.businesses?.connect_account?.add){
    <p-button label="Create Connect Account" icon="pi pi-plus" (click)="this.connectAccountDialog = true;" raised />
    <p-button label="Add Connect Account ID" icon="pi pi-plus" (click)="openDialogToAddAccountId()" raised />
    }
</div>

<div class="m-0 p-0" *ngIf="connectAccounts">
    <div class="flex flex-col sm:flex-row justify-between gap-2">
        <div class="flex flex-col sm:flex-row sm:justify-start  gap-2">
            @if(connectAccounts?.s_account_id &&
            connectAccounts?.stripe_connect_account?.requirements?.disabled_reason == null ||
            connectAccounts?.stripe_connect_account?.requirements?.currently_due.length){
            <p-tag icon="pi pi-check" severity="success" value="Account is live" />
            }
            @if(business?.account_status == 'pending'){
            <p-tag icon="pi pi-exclamation-triangle" severity="warning" value="Account is pending" />
            }
            @if(business?.account_status == 'restricted'){
            <p-tag icon="pi pi-exclamation-triangle" severity="danger" value="Account is restricted" />
            }
            @if(business?.account_status == 'complete'){
            <p-tag icon="pi pi-check" severity="success" value="Account is complete" />
            }
            @if(connectAccounts.stripe_connect_account?.requirements?.disabled_reason!=null){
            <p-button label="Show errors" icon="pi pi-exclamation-triangle" severity="danger" />
            }
        </div>
        <div class="flex flex-col sm:flex-row  gap-2">
            @if(connectAccounts?.account_balace?.available[0]?.amount >= 0){
            <p-tag severity="success" value="In transists or Available
                {{(connectAccounts?.account_balace?.available[0]?.amount/100) | currency:'GBP':'symbol':'1.2-2' }}" />
            }

            @if(connectAccounts?.account_balace?.instant_available[0]?.amount >= 0){
            <p-tag severity="success"
                value="Future Payouts
                {{(connectAccounts?.account_balace?.instant_available[0]?.amount/100) | currency:'GBP':'symbol':'1.2-2' }}" />
            }

            @if(connectAccounts?.account_balace?.pending[0]?.amount >= 0){
            <p-tag severity="warn" value="Pending Balance 
                {{(connectAccounts?.account_balace?.pending[0]?.amount/100) | currency:'GBP':'symbol':'1.2-2' }}" />
            }

            @if((authService._superman || authService._permissions?.businesses?.connect_account?.load_money) &&
            business.s_account_id){
            <p-button (click)="loadMoneyOp.toggle($event)" icon="pi pi-plus" label="Load Money" severity="info" />
            <p-popover #loadMoneyOp [dismissable]="true" (onHide)="LoadMoneyAmount.reset()">
                <div class="flex flex-col gap-4 w-[25rem]">
                    <p-toolbar [style]="{'border': 'none','padding': '0'}">
                        <ng-template #start>
                            <p class="font-medium font-bold text-2xl">Load money to business</p>
                        </ng-template>
                        <ng-template #end>
                            <p-button severity="secondary" icon="pi pi-times" text
                                (click)="loadMoneyOp.toggle($event)" />
                        </ng-template>
                    </p-toolbar>
                    <form (ngSubmit)="loadMoney()">
                        <div class="grid grid-cols-1 md:grid-cols-1 gap-4">
                            <div>
                                <label for="amount" class="required-label block font-bold mb-3">Amount</label>
                                <p-inputnumber [formControl]="LoadMoneyAmount" aria-label="Amount" inputId="amount"
                                    placeholder="Amount" mode="decimal" [minFractionDigits]="2" required fluid />
                                <app-form-error [control]="LoadMoneyAmount" [controlName]="'Amount'"
                                    [apiErrorType]="'amount'" />
                            </div>
                            <!-- submit  -->
                            <button type="submit" style="display: none"></button>
                        </div>
                        <div class="flex justify-end gap-2 mt-6 border-t border-gray-200 pt-2">
                            <p-button label="Cancel" icon="pi pi-times" text (click)="loadMoneyOp.toggle($event)" />
                            <p-button label="Save" icon="pi pi-check" (click)="loadMoney()" />
                        </div>
                    </form>
                </div>
            </p-popover>
            }

            @if((authService._superman || authService._permissions?.businesses?.connect_account?.debit_money) &&
            (business.s_account_id &&
            connectAccounts?.account_balace)){
            <p-button (click)="DebitOp.toggle($event)" icon="pi pi-money-bill" label="Debit" severity="danger" />
            <p-popover #DebitOp [dismissable]="true" (onHide)="DebitAmount.reset()">
                <div class="flex flex-col gap-4 w-[25rem]">
                    <p-toolbar [style]="{'border': 'none','padding': '0'}">
                        <ng-template #start>
                            <p class="font-medium font-bold text-2xl">Debit to business</p>
                        </ng-template>
                        <ng-template #end>
                            <p-button severity="secondary" icon="pi pi-times" text (click)="DebitOp.toggle($event)" />
                        </ng-template>
                    </p-toolbar>
                    <form (ngSubmit)="debitMoney()">
                        <div class="grid grid-cols-1 md:grid-cols-1 gap-4">
                            <div>
                                <label for="DebitAmount" class="required-label block font-bold mb-3">Amount</label>
                                <p-inputnumber [formControl]="DebitAmount" aria-label="Amount" inputId="DebitAmount"
                                    placeholder="Amount" mode="decimal" [minFractionDigits]="2" required fluid />
                                <app-form-error [control]="DebitAmount" [controlName]="'Amount'"
                                    [apiErrorType]="'amount'" />
                            </div>
                            <!-- submit  -->
                            <button type="submit" style="display: none"></button>
                        </div>
                        <div class="flex justify-end gap-2 mt-6 border-t border-gray-200 pt-2">
                            <p-button label="Cancel" icon="pi pi-times" text (click)="DebitOp.toggle($event)" />
                            <p-button label="Save" icon="pi pi-check" (click)="debitMoney()" />
                        </div>
                    </form>
                </div>
            </p-popover>
            }

            @if((authService._superman || authService._permissions?.businesses?.connect_account?.edit) &&
            connectAccounts?.stripe_connect_account?.requirements?.disabled_reason != null
            || connectAccounts?.stripe_connect_account?.requirements?.currently_due.length ||
            connectAccounts?.s_account_id == null){
            <p-button icon="pi pi-pencil" label="{{connectAccounts ? 'Edit':'Create'}} Connect Account" outlined
                (click)="this.connectAccountDialog = true; isEdit = true;"></p-button>
            }
            @if( (authService._superman || authService._permissions?.businesses?.connect_account?.generate) &&
            ((connectAccounts &&
            connectAccounts.s_account_id==null) ||
            (connectAccounts.stripe_connect_account?.requirements?.disabled_reason!=null ||
            connectAccounts?.stripe_connect_account?.requirements?.currently_due.length))){
            <p-button icon="pi pi-plus" label="Generate" (click)="generateConnectAccount()" />
            }
        </div>
    </div>

    <p-panel header="Business Details" [toggleable]="true" styleClass="mt-4">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
            <dl class="divide-y divide-gray-200">
                <div class="py-3 flex items-center border-b border-gray-200">
                    <i class="fas fa-id-card-clip text-primary-500 mr-3 w-6 h-6 flex items-center justify-center"></i>
                    <dt class="font-bold w-22 sm:w-42 md:w-32 lg:w-52">Account ID</dt>
                    <dt class="font-bold w-6 hidden sm:block">:</dt>
                    <dd class="flex-1  break-all">{{ connectAccounts?.s_account_id || '--' }}</dd>
                </div>
            </dl>
            <dl class="divide-y divide-gray-200">
                <div class="py-3 flex items-center border-b border-gray-200">
                    <i class="fas fa-signature text-primary-500 mr-3 w-6 h-6 flex items-center justify-center"></i>
                    <dt class="font-bold w-22 sm:w-42 md:w-32 lg:w-52">Name</dt>
                    <dt class="font-bold w-6 hidden sm:block">:</dt>
                    <dd class="flex-1  break-all">{{ connectAccounts?.company?.name || '--' }}</dd>
                </div>
            </dl>
            <dl class="divide-y divide-gray-200">
                <div class="py-3 flex items-center border-b border-gray-200">
                    <i class="fas fa-envelope text-primary-500 mr-3 w-6 h-6 flex items-center justify-center"></i>
                    <dt class="font-bold w-22 sm:w-42 md:w-32 lg:w-52">Email</dt>
                    <dt class="font-bold w-6 hidden sm:block">:</dt>
                    <dd class="flex-1  break-all">{{ connectAccounts?.company?.email || '--' }}</dd>
                </div>
            </dl>
            <dl class="divide-y divide-gray-200">
                <div class="py-3 flex items-center border-b border-gray-200">
                    <i class="fas fa-phone text-primary-500 mr-3 w-6 h-6 flex items-center justify-center"></i>
                    <dt class="font-bold w-22 sm:w-42 md:w-32 lg:w-52">Phone number</dt>
                    <dt class="font-bold w-6 hidden sm:block">:</dt>
                    <dd class="flex-1  break-all">{{ connectAccounts?.company?.phone_number || '--' }}</dd>
                </div>
            </dl>
            <dl class="divide-y divide-gray-200">
                <div class="py-3 flex items-center border-b border-gray-200">
                    <i class="fas fa-address-card text-primary-500 mr-3 w-6 h-6 flex items-center justify-center"></i>
                    <dt class="font-bold w-22 sm:w-42 md:w-32 lg:w-52">Address</dt>
                    <dt class="font-bold w-6 hidden sm:block">:</dt>
                    <dd class="flex-1  break-all">{{ connectAccounts?.company?.line1 }} {{
                        connectAccounts?.company?.city }} {{ connectAccounts?.company?.state }} {{
                        connectAccounts?.company?.postal_code }}</dd>
                </div>
            </dl>
            <dl class="divide-y divide-gray-200">
                <div class="py-3 flex items-center border-b border-gray-200">
                    <i class="fas fa-link text-primary-500 mr-3 w-6 h-6 flex items-center justify-center"></i>
                    <dt class="font-bold w-22 sm:w-42 md:w-32 lg:w-52">URL</dt>
                    <dt class="font-bold w-6 hidden sm:block">:</dt>
                    <dd class="flex-1  break-all">{{ connectAccounts?.company?.url || '--' }}</dd>
                </div>
            </dl>
            <dl class="divide-y divide-gray-200">
                <div class="py-3 flex items-center border-b border-gray-200">
                    <i class="fas fa-globe text-primary-500 mr-3 w-6 h-6 flex items-center justify-center"></i>
                    <dt class="font-bold w-22 sm:w-42 md:w-32 lg:w-52">Country</dt>
                    <dt class="font-bold w-6 hidden sm:block">:</dt>
                    <dd class="flex-1  break-all">{{ connectAccounts?.country || '--' }}</dd>
                </div>
            </dl>
        </div>
    </p-panel>

    @if(connectAccounts?.external_account){
    <p-panel header="External Account Details" [toggleable]="true" styleClass="mt-4">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
            <dl class="divide-y divide-gray-200">
                <div class="py-3 flex items-center border-b border-gray-200">
                    <i class="fas fa-signature text-primary-500 mr-3 w-6 h-6 flex items-center justify-center"></i>
                    <dt class="font-bold w-22 sm:w-42 md:w-32 lg:w-52">Name</dt>
                    <dt class="font-bold w-6 hidden sm:block">:</dt>
                    <dd class="flex-1  break-all">{{ connectAccounts?.external_account?.account_holder_name
                        || '--' }}</dd>
                </div>
            </dl>
            <dl class="divide-y divide-gray-200">
                <div class="py-3 flex items-center border-b border-gray-200">
                    <i
                        class="fas fa-building-columns text-primary-500 mr-3 w-6 h-6 flex items-center justify-center"></i>
                    <dt class="font-bold w-22 sm:w-42 md:w-32 lg:w-52">Type</dt>
                    <dt class="font-bold w-6 hidden sm:block">:</dt>
                    <dd class="flex-1  break-all">{{ connectAccounts?.external_account?.account_holder_type
                        || '--' }}</dd>
                </div>
            </dl>
            <dl class="divide-y divide-gray-200">
                <div class="py-3 flex items-center border-b border-gray-200">
                    <i class="fas fa-user-tie text-primary-500 mr-3 w-6 h-6 flex items-center justify-center"></i>
                    <dt class="font-bold w-22 sm:w-42 md:w-32 lg:w-52">Account Number</dt>
                    <dt class="font-bold w-6 hidden sm:block">:</dt>
                    <dd class="flex-1  break-all">{{ connectAccounts?.external_account?.account_number ||
                        '--' }}</dd>
                </div>
            </dl>
            <dl class="divide-y divide-gray-200">
                <div class="py-3 flex items-center border-b border-gray-200">
                    <i class="fas fa-user-tie text-primary-500 mr-3 w-6 h-6 flex items-center justify-center"></i>
                    <dt class="font-bold w-22 sm:w-42 md:w-32 lg:w-52">Sort Code</dt>
                    <dt class="font-bold w-6 hidden sm:block">:</dt>
                    <dd class="flex-1  break-all">{{ connectAccounts?.external_account?.routing_number ||
                        '--' }}</dd>
                </div>
            </dl>
            <dl class="divide-y divide-gray-200">
                <div class="py-3 flex items-center border-b border-gray-200">
                    <i class="fas fa-globe text-primary-500 mr-3 w-6 h-6 flex items-center justify-center"></i>
                    <dt class="font-bold w-22 sm:w-42 md:w-32 lg:w-52">Country</dt>
                    <dt class="font-bold w-6 hidden sm:block">:</dt>
                    <dd class="flex-1  break-all">{{connectAccounts?.external_account?.country || '--' }} /
                        {{connectAccounts?.external_account?.currency || '--' }}</dd>
                </div>
            </dl>
        </div>
    </p-panel>
    }

    @if(connectAccounts?.account_opener){
    <p-panel header="Account Opener/Representative Details" [toggleable]="true" styleClass="mt-4">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
            <dl class="divide-y divide-gray-200">
                <div class="py-3 flex items-center border-b border-gray-200">
                    <i class="fas fa-user text-primary-500 mr-3 w-6 h-6 flex items-center justify-center"></i>
                    <dt class="font-bold w-22 sm:w-42 md:w-32 lg:w-52">Name</dt>
                    <dt class="font-bold w-6 hidden sm:block">:</dt>
                    <dd class="flex-1  break-all">{{ connectAccounts?.account_opener?.first_name || '--' }}
                        {{ connectAccounts?.account_opener?.last_name || '--' }}</dd>
                </div>
            </dl>
            <dl class="divide-y divide-gray-200">
                <div class="py-3 flex items-center border-b border-gray-200">
                    <i class="fas fa-envelope text-primary-500 mr-3 w-6 h-6 flex items-center justify-center"></i>
                    <dt class="font-bold w-22 sm:w-42 md:w-32 lg:w-52">Email</dt>
                    <dt class="font-bold w-6 hidden sm:block">:</dt>
                    <dd class="flex-1  break-all">{{ connectAccounts?.account_opener?.email || '--' }}</dd>
                </div>
            </dl>
            <dl class="divide-y divide-gray-200">
                <div class="py-3 flex items-center border-b border-gray-200">
                    <i class="fas fa-phone text-primary-500 mr-3 w-6 h-6 flex items-center justify-center"></i>
                    <dt class="font-bold w-22 sm:w-42 md:w-32 lg:w-52">Phone number</dt>
                    <dt class="font-bold w-6 hidden sm:block">:</dt>
                    <dd class="flex-1  break-all">{{ connectAccounts?.account_opener?.phone_number || '--'
                        }}
                    </dd>
                </div>
            </dl>
            <dl class="divide-y divide-gray-200">
                <div class="py-3 flex items-center border-b border-gray-200">
                    <i class="fas fa-address-card text-primary-500 mr-3 w-6 h-6 flex items-center justify-center"></i>
                    <dt class="font-bold w-22 sm:w-42 md:w-32 lg:w-52">Address</dt>
                    <dt class="font-bold w-6 hidden sm:block">:</dt>
                    <dd class="flex-1  break-all">{{ connectAccounts?.account_opener?.line1 }} {{
                        connectAccounts?.account_opener?.city }} {{ connectAccounts?.account_opener?.state }}
                        {{ connectAccounts?.account_opener?.postal_code }}</dd>
                </div>
            </dl>
            <dl class="divide-y divide-gray-200">
                <div class="py-3 flex items-center border-b border-gray-200">
                    <i class="fas fa-calendar text-primary-500 mr-3 w-6 h-6 flex items-center justify-center"></i>
                    <dt class="font-bold w-22 sm:w-42 md:w-32 lg:w-52">Birth date</dt>
                    <dt class="font-bold w-6 hidden sm:block">:</dt>
                    <dd class="flex-1  break-all">{{ connectAccounts?.account_opener?.dob || '--' }}</dd>
                </div>
            </dl>
        </div>
    </p-panel>
    }

    @if(connectAccounts?.account_owner){
    <p-panel header="Account Owner Details" [toggleable]="true" styleClass="mt-4">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
            <dl class="divide-y divide-gray-200">
                <div class="py-3 flex items-center border-b border-gray-200">
                    <i class="fas fa-user text-primary-500 mr-3 w-6 h-6 flex items-center justify-center"></i>
                    <dt class="font-bold w-22 sm:w-42 md:w-32 lg:w-52">Name</dt>
                    <dt class="font-bold w-6 hidden sm:block">:</dt>
                    <dd class="flex-1  break-all">{{ connectAccounts?.account_owner?.first_name || '--' }}
                        {{ connectAccounts?.account_owner?.last_name || '--' }}</dd>
                </div>
            </dl>
            <dl class="divide-y divide-gray-200">
                <div class="py-3 flex items-center border-b border-gray-200">
                    <i class="fas fa-envelope text-primary-500 mr-3 w-6 h-6 flex items-center justify-center"></i>
                    <dt class="font-bold w-22 sm:w-42 md:w-32 lg:w-52">Email</dt>
                    <dt class="font-bold w-6 hidden sm:block">:</dt>
                    <dd class="flex-1  break-all">{{ connectAccounts?.account_owner?.email || '--' }}</dd>
                </div>
            </dl>
            <dl class="divide-y divide-gray-200">
                <div class="py-3 flex items-center border-b border-gray-200">
                    <i class="fas fa-phone text-primary-500 mr-3 w-6 h-6 flex items-center justify-center"></i>
                    <dt class="font-bold w-22 sm:w-42 md:w-32 lg:w-52">Phone number</dt>
                    <dt class="font-bold w-6 hidden sm:block">:</dt>
                    <dd class="flex-1  break-all">{{ connectAccounts?.account_owner?.phone_number || '--'
                        }}
                    </dd>
                </div>
            </dl>
            <dl class="divide-y divide-gray-200">
                <div class="py-3 flex items-center border-b border-gray-200">
                    <i class="fas fa-address-card text-primary-500 mr-3 w-6 h-6 flex items-center justify-center"></i>
                    <dt class="font-bold w-22 sm:w-42 md:w-32 lg:w-52">Address</dt>
                    <dt class="font-bold w-6 hidden sm:block">:</dt>
                    <dd class="flex-1  break-all">{{ connectAccounts?.account_owner?.line1 }} {{
                        connectAccounts?.account_owner?.city }} {{ connectAccounts?.account_owner?.state
                        }} {{ connectAccounts?.account_owner?.postal_code }}</dd>
                </div>
            </dl>
            <dl class="divide-y divide-gray-200">
                <div class="py-3 flex items-center border-b border-gray-200">
                    <i class="fas fa-calendar text-primary-500 mr-3 w-6 h-6 flex items-center justify-center"></i>
                    <dt class="font-bold w-22 sm:w-42 md:w-32 lg:w-52">Birth date</dt>
                    <dt class="font-bold w-6 hidden sm:block">:</dt>
                    <dd class="flex-1  break-all">{{ connectAccounts?.account_owner?.dob || '--' }}</dd>
                </div>
            </dl>
        </div>
    </p-panel>
    }

    @if(connectAccounts?.stripe_connect_account?.settings?.payouts?.schedule){
    <p-panel header="Payout Schedule" [toggleable]="true" styleClass="mt-4">
        <p-toolbar [style]="{'border': 'none','padding': '0'}">
            <ng-template #start>
            </ng-template>

            <ng-template #end>
                <p-button label="Edit Payout Schedule" icon="pi pi-pencil" class="mr-2"
                    (click)="openDialogToEditPayoutSchedule()" />
                <p-button (click)="payoutOp.toggle($event)" label="Instant Payout" icon="pi pi-money-bill"
                    severity="success" class="mr-2" />
                <p-popover #payoutOp [dismissable]="true" (onHide)="payoutAmount.reset()">
                    <div class="flex flex-col gap-4 w-[25rem]">
                        <p-toolbar [style]="{'border': 'none','padding': '0'}">
                            <ng-template #start>
                                <p class="font-medium font-bold text-2xl">Instant Payout</p>
                            </ng-template>
                            <ng-template #end>
                                <p-button severity="secondary" icon="pi pi-times" text
                                    (click)="payoutOp.toggle($event)" />
                            </ng-template>
                        </p-toolbar>
                        <form (ngSubmit)="instantPayout()">
                            <div class="grid grid-cols-1 md:grid-cols-1 gap-4">
                                <div>
                                    <label for="payoutAmount" class="required-label block font-bold mb-3">Amount</label>
                                    <p-inputnumber [formControl]="payoutAmount" aria-label="Amount"
                                        inputId="payoutAmount" placeholder="Amount" mode="decimal"
                                        [minFractionDigits]="2" required fluid />
                                </div>
                            </div>
                            <button type="submit" style="display: none"></button>
                            <div class="flex justify-end gap-2 mt-6 border-t border-gray-200 pt-2">
                                <p-button label="Cancel" icon="pi pi-times" text (click)="payoutOp.toggle($event)" />
                                <p-button label="Payout" icon="pi pi-money-bill" (click)="instantPayout()" />
                            </div>
                        </form>
                    </div>
                </p-popover>
                <p-button label="Payout" icon="pi pi-money-bill" severity="info" class="mr-2"
                    (click)="showPayoutModal()" />
            </ng-template>
        </p-toolbar>
        <div class="grid grid-cols-1 md:grid-cols-1 gap-2">
            <dl class="divide-y divide-gray-200">
                <div class="py-3 flex items-center border-b border-gray-200">
                    <i class="fas fa-calendar text-primary-500 mr-3 w-6 h-6 flex items-center justify-center"></i>
                    <dt class="font-bold w-22 sm:w-42 md:w-32 lg:w-52">Payout Schedule</dt>
                    <dt class="font-bold w-6 hidden sm:block">:</dt>
                    <dd class="flex-1  break-all">
                        <ng-container
                            *ngIf="connectAccounts?.stripe_connect_account?.settings?.payouts?.schedule?.interval!='manual'">
                            <div class="fs-16">Automatic
                                {{connectAccounts?.stripe_connect_account?.settings?.payouts?.schedule?.interval}}
                                <ng-container>
                                    & delay payouts by
                                    {{connectAccounts?.stripe_connect_account?.settings?.payouts?.schedule?.delay_days}}
                                    days
                                </ng-container>
                                <ng-container
                                    *ngIf="connectAccounts?.stripe_connect_account?.settings?.payouts?.schedule?.interval=='weekly'">
                                    & on every
                                    {{connectAccounts?.stripe_connect_account?.settings?.payouts?.schedule?.weekly_anchor|titlecase}}
                                </ng-container>
                                <ng-container
                                    *ngIf="connectAccounts?.stripe_connect_account?.settings?.payouts?.schedule?.interval=='monthly'">
                                    & on every
                                    {{connectAccounts?.stripe_connect_account?.settings?.payouts?.schedule?.monthly_anchor}}
                                </ng-container>
                            </div>

                        </ng-container>
                        <ng-container
                            *ngIf="connectAccounts?.stripe_connect_account?.settings?.payouts?.schedule?.interval=='manual'">
                            <div class="fs-16">Manual</div>
                        </ng-container>
                    </dd>
                </div>
            </dl>
        </div>
    </p-panel>
    }

    <p-panel header="External Account Details" [toggleable]="true" styleClass="mt-4">
        <p-table #dt [value]="connectAccounts?.external_accounts" [rowHover]="true" dataKey="id"
            [tableStyle]="{ 'min-width': '60rem' }" [scrollable]="true" scrollHeight="60vh">
            <ng-template #caption>
                <p-toolbar [style]="{'border': 'none','padding': '0'}">
                    <ng-template #start>
                        <p class="font-medium font-bold text-2xl">External Accounts</p>
                    </ng-template>
                    <ng-template #end>
                        @if((authService._superman ||
                        authService._permissions?.businesses?.connect_account?.add_external_account)){
                        <p-button label="Add External Account" icon="pi pi-plus" severity="secondary" class="mr-2"
                            (click)="openDialogToAddExternalAccount(false)" />
                        }
                    </ng-template>
                </p-toolbar>
            </ng-template>
            <ng-template #header>
                <tr>
                    <th pSortableColumn="account_holder_name" pFrozenColumn>Name <p-sortIcon
                            field="account_holder_name" /></th>
                    <th pSortableColumn="account_number">Number <p-sortIcon field="account_number" /></th>
                    <th pSortableColumn="routing_number">Sort Code <p-sortIcon field="routing_number" /></th>
                    <th>Type</th>
                    <th style="text-align: center;">Customer Show</th>
                    <th>Action</th>
                </tr>
            </ng-template>
            <ng-template #body let-externalAccount>
                <tr>
                    <td pFrozenColumn>{{externalAccount.account_holder_name}}</td>
                    <td>{{externalAccount.account_number}}</td>
                    <td>{{externalAccount.routing_number}}</td>
                    <td>{{externalAccount.account_holder_type}}</td>
                    <td style="text-align: center;">
                        <p-toggleswitch [ngModel]="externalAccount.is_customer ? true : false"
                            (onChange)="toggleStatusExternalAccount($event, externalAccount)" />
                    </td>
                    <td>
                        @if(externalAccount.default){
                        <p-button label="Default" icon="pi pi-check" class="mr-2" [rounded]="true" raised
                            severity="info" />
                        } @else {
                        <p-button label="Make Default" class="mr-2" [rounded]="true" pTooltip="Make Default"
                            tooltipPosition="top" (click)="makeDefaultExternalAccount(externalAccount)"
                            severity="secondary" />
                        }
                        @if((authService._superman ||
                        authService._permissions?.businesses?.connect_account?.edit_external_account)){
                        <p-button icon="pi pi-pencil" class="mr-2" [rounded]="true" [outlined]="true"
                            (click)="openDialogToAddExternalAccount(true, externalAccount)" />
                        }
                        @if( (authService._superman ||
                        authService._permissions?.businesses?.connect_account?.delete_external_account) &&
                        !externalAccount.default){
                        <p-button icon="pi pi-trash" severity="danger" [rounded]="true" [outlined]="true"
                            (click)="deleteExternalAccount(externalAccount)" />
                        }
                    </td>
                </tr>

            </ng-template>
            <ng-template #emptymessage>
                <tr>
                    <td colspan="5" style="text-align: center !important;">No records found</td>
                </tr>
            </ng-template>
        </p-table>
    </p-panel>

    <p-panel header="Financial Details" [toggleable]="true" styleClass="mt-4">
        <p-table #dt [value]="financial_accounts" [rowHover]="true" dataKey="id" [tableStyle]="{ 'min-width': '70rem' }"
            [scrollable]="true" scrollHeight="60vh">
            <ng-template #caption>
                <p-toolbar [style]="{'border': 'none','padding': '0'}">
                    <ng-template #start>
                        <p class="font-medium font-bold text-2xl">Financial Accounts</p>
                    </ng-template>

                    <ng-template #end>
                        @if((authService._superman ||
                        authService._permissions?.businesses?.connect_account?.add_financial_account)){
                        <p-button label="Add Financial Connection" icon="pi pi-plus" severity="secondary" class="mr-2"
                            (click)="openDialogToAddFinancialConnection()" />
                        }
                        <!-- hardh Refresh -->
                        <p-button label="Hard Refresh" icon="pi pi-refresh" severity="help" class="mr-2"
                            (click)="fetchFinancialConnectionsHardRefresh()" />
                    </ng-template>
                </p-toolbar>
            </ng-template>
            <ng-template #header>
                <tr>
                    <th pSortableColumn="account_holder_name" pFrozenColumn>
                        Account Holder Name
                        <p-sortIcon field="account_holder_name" />
                    </th>
                    <!-- <th pSortableColumn="bank_name">
                        Bank Name
                        <p-sortIcon field="bank_name" />
                    </th> -->
                    <th pSortableColumn="account_number">
                        Account Number
                        <p-sortIcon field="account_number" />
                    </th>
                    <th pSortableColumn="routing_number">
                        Sort Code
                        <p-sortIcon field="routing_number" />
                    </th>
                    <th style="text-align: center;" pSortableColumn="balance">
                        Balance
                        <p-sortIcon field="balance" />
                    </th>
                    <th pSortableColumn="status">
                        Status
                        <p-sortIcon field="status" />
                    </th>
                    <th>Action</th>
                </tr>
            </ng-template>
            <ng-template #body let-financialAccount>
                <tr>
                    @if(financialAccount?.financial_addresses?.account_holder_name){
                    <td pFrozenColumn>{{financialAccount?.financial_addresses?.account_holder_name}}
                        <span *ngIf="financialAccount?.metadata?.nickname">
                            ({{ financialAccount.metadata.nickname }})
                        </span>
                    </td>
                    } @else {
                    <td pFrozenColumn>--</td>
                    }
                    <!-- <td>{{financialAccount?.financial_addresses?.bank_name || '--' }}</td> -->
                    <td>{{financialAccount?.financial_addresses?.account_number || '--' }}</td>
                    <td>{{financialAccount?.financial_addresses?.routing_number || '--' }}</td>
                    <td style="text-align: center;">
                        @if( (authService._superman ||
                        authService._permissions?.businesses?.connect_account?.view_balance_financial_account) &&
                        financialAccount?.financial_addresses){
                        <p-button raised rounded icon="pi pi-eye" severity="info" pTooltip="View Balance"
                            tooltipPosition="top" (click)="viewFinancialAccountBalance(financialAccount)" />
                        } @else {
                        <p-tag severity="danger" value="Not Available" />
                        }
                    </td>
                    <td>
                        @if(financialAccount?.financial_addresses){
                        <p-button [label]="financialAccount?.financial_addresses?.status | titlecase" raised rounded
                            [severity]="getSeverity(financialAccount?.financial_addresses?.status)" />
                        } @else {
                        --
                        }
                    </td>
                    <td>
                        @if(!financialAccount?.financial_addresses){
                        @if((authService._superman ||
                        authService._permissions?.businesses?.connect_account?.add_address_financial_account)){
                        <p-button label="Address" icon="pi pi-plus" severity="info" class="mr-2" outlined
                            (click)="openDialogToAddAddress(financialAccount)" />
                        }
                        } @else {
                        @if((authService._superman ||
                        authService._permissions?.businesses?.connect_account?.credit_money_financial_account)){
                        <p-button icon="pi pi-credit-card" severity="success" class="mr-2" [rounded]="true" outlined
                            pTooltip="Credit Money" tooltipPosition="top"
                            (click)="OpenCreditMoneyModal(financialAccount)">
                        </p-button>
                        }
                        <!-- Fund Transfer -->
                        @if((authService._superman ||
                        authService._permissions?.businesses?.connect_account?.fund_transfer_financial_account)){
                        <p-button severity="info" class="mr-2" [rounded]="true" outlined pTooltip="Fund Transfer"
                            tooltipPosition="top" (click)="fundTransferFromFinancialAccount(financialAccount)">
                            <i class="fa-solid fa-money-bill-transfer" style="color: #2d89ef;"></i>
                        </p-button>
                        }
                        <!--Transfer between other business financial accounts -->
                        @if((authService._superman ||
                        authService._permissions?.businesses?.connect_account?.fund_transfer_financial_account)){
                        <p-button severity="success" class="mr-2" [rounded]="true" outlined
                            pTooltip="Other Business Account Transfer"
                            (click)="transferBetweenOtherBusinessAccounts(financialAccount)">
                            <i class="fa-solid fa-right-left" style="color: #28a745;"></i>
                        </p-button>
                        }
                        }
                        @if((authService._superman ||
                        authService._permissions?.businesses?.connect_account?.delete_financial_account)){
                        <p-button icon="pi pi-trash" class="mr-2" pTooltip="Delete" tooltipPosition="top"
                            severity="danger" [rounded]="true" [outlined]="true"
                            (click)="deleteFinancialConnection(financialAccount)" />
                        }
                        <!-- Download Statement -->
                        <p-button pTooltip="Download Statement" class="mr-2" [rounded]="true" [outlined]="true"
                            tooltipPosition="top" icon="pi pi-download" severity="info" class="mr-2"
                            (click)="downloadStatementOp.toggle($event)" />
                        <p-popover #downloadStatementOp [dismissable]="true" (onHide)="statementDateRange.reset()">
                            <div class="flex flex-col gap-4 w-[25rem]">
                                <p-toolbar [style]="{'border': 'none','padding': '0'}">
                                    <ng-template #start>
                                        <p class="font-medium font-bold text-xl">Download Statement
                                            ({{financialAccount?.metadata.nickname}})</p>
                                    </ng-template>
                                    <ng-template #end>
                                        <p-button icon="pi pi-times" text (click)="downloadStatementOp.hide()" />
                                    </ng-template>
                                </p-toolbar>
                                <form (ngSubmit)="downloadStatement(financialAccount)">
                                    <div class="grid grid-cols-1 md:grid-cols-1 gap-4">
                                        <div>
                                            <label for="date_range" class="required-label block font-bold mb-3">Date
                                                Range </label>
                                            <p-datepicker [formControl]="statementDateRange" selectionMode="range"
                                                inputId="date_range" [showClear]="true" styleClass="w-full"
                                                placeholder="Select a date range" dateFormat="dd-mm-yy"
                                                [appendTo]="'body'" />
                                            <app-form-error [control]="statementDateRange" [controlName]="'Date Range'"
                                                [apiErrorType]="'date_range'" />
                                        </div>
                                        <!-- submit  -->
                                        <button type="submit" style="display: none"></button>
                                    </div>
                                    <div class="flex justify-end gap-2 mt-6 border-t border-gray-200 pt-2">
                                        <p-button label="Cancel" icon="pi pi-times" text
                                            (click)="downloadStatementOp.toggle($event)" />
                                        <p-button label="Download" icon="pi pi-download"
                                            (click)="downloadStatement(financialAccount)" />
                                    </div>
                                </form>
                            </div>
                        </p-popover>

                    </td>
                </tr>

            </ng-template>

            <ng-template #emptymessage>
                <tr>
                    <td colspan="4" style="text-align: center !important;">No records found</td>
                </tr>
            </ng-template>
        </p-table>
    </p-panel>

    <p-panel header="Bank Account Details" [toggleable]="true" styleClass="mt-4">
        <p-table #dt [value]="bank_accounts" [rowHover]="true" dataKey="id" [tableStyle]="{ 'min-width': '60rem' }"
            [scrollable]="true" scrollHeight="60vh">
            <ng-template #caption>
                <p-toolbar [style]="{'border': 'none','padding': '0'}">
                    <ng-template #start>
                        <p class="font-medium font-bold text-2xl">Bank Accounts</p>
                    </ng-template>
                    <ng-template #end>
                        @if((authService._superman ||
                        authService._permissions?.businesses?.connect_account?.add_bank_account)){
                        <p-button label="Add Bank Account" icon="pi pi-plus" severity="secondary" class="mr-2"
                            (click)="openDialogToAddBankAccount()" />
                        }
                        <!-- hardh Refresh -->
                        <p-button label="Hard Refresh" icon="pi pi-refresh" severity="help" class="mr-2"
                            (click)="fetchBankAccountsHardRefresh()" />
                    </ng-template>
                </p-toolbar>
            </ng-template>
            <ng-template #header>
                <tr>
                    <th pSortableColumn="account_holder_name" pFrozenColumn>Account Holder Name <p-sortIcon
                            field="account_holder_name" /></th>
                    <th pSortableColumn="bank_name">Bank Name <p-sortIcon field="bank_name" /></th>
                    <th pSortableColumn="last4"> Account Number <p-sortIcon field="last4" /></th>
                    <th pSortableColumn="sort_code">Sort Code <p-sortIcon field="sort_code" /></th>
                    <th>Action</th>
                </tr>
            </ng-template>
            <ng-template #body let-bankAccount>
                <tr>
                    <td pFrozenColumn>{{bankAccount?.account_holder_name}}</td>
                    <td>{{bankAccount?.bank_name}}</td>
                    <td>XXXX{{bankAccount?.last4}}</td>
                    <td>{{bankAccount?.sort_code}}</td>
                    <td>
                        @if(!bankAccount?.archived){
                        @if((authService._superman ||
                        authService._permissions?.businesses?.connect_account?.fund_transfer_bank_account)){
                        <p-button severity="info" class="mr-2" [rounded]="true" outlined pTooltip="Fund Transfer"
                            tooltipPosition="top" disabled>
                            <i class="fa-solid fa-money-bill-transfer" style="color: #2d89ef;"></i>
                        </p-button>
                        }
                        @if ((authService._superman ||
                        authService._permissions?.businesses?.connect_account?.edit_bank_account)) {
                        <p-button icon="pi pi-pencil" class="mr-2" [rounded]="true" [outlined]="true"
                            (click)="openDialogToAddBankAccount(bankAccount)" />
                        }

                        @if((authService._superman ||
                        authService._permissions?.businesses?.connect_account?.delete_bank_account)){
                        <p-button icon="pi pi-trash" severity="danger" [rounded]="true" [outlined]="true"
                            (click)="deleteBankAccount(bankAccount)" />
                        }
                        }
                        @else {
                        <p-tag severity="danger" value="Archived" />
                        }
                    </td>
                </tr>

            </ng-template>
            <ng-template #emptymessage>
                <tr>
                    <td colspan="5" style="text-align: center !important;">No records found</td>
                </tr>
            </ng-template>
        </p-table>
    </p-panel>
</div>

@if(connectAccountDialog){
<app-create-connect-account [(connectAccountDialog)]="connectAccountDialog" (save)="handleSave($event)"
    [connectAccount]="connectAccounts" [isEdit]="isEdit" [business]="business"></app-create-connect-account>
}

<p-confirmdialog [style]="{ width: '450px' }" />