import { Component, OnInit, inject } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ApiService } from '../../../../core/services/api.service';
import { MessageService } from 'primeng/api';
import { DialogService, DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { PrimengModules } from '../../../../core/utils/primeng';
import { LoaderService } from '../../../../core/services/loader.service';
import { ServerApiService } from '../../../../core/services/server-api.service';

@Component({
    selector: 'app-credit-money-modal',
    imports: [PrimengModules],
    template: `
                <div class="w-full">
                    <form [formGroup]="creditMoneyForm">
                        <div class="grid grid-cols-1 md:grid-cols-1 gap-4">
                        <div>
                                <label for="currency" class="required-label block font-bold mb-3">Currency</label>
                                <p-select [options]="currencies" optionLabel="name" optionValue="id" [showClear]="true"
                                    formControlName="currency" placeholder="Select a currency" [appendTo]="'body'" required
                                    fluid />
                                <app-form-error [control]="creditMoneyForm.controls['currency']" [controlName]="'Currency'"
                                    [apiErrorType]="'currency'" />
                            </div>
                            <!-- <div>
                                <label for="network" class="required-label block font-bold mb-3">Network</label>
                                <p-select [options]="networks" optionLabel="name" optionValue="id" [showClear]="true"
                                    formControlName="network" placeholder="Select a network" [appendTo]="'body'" required
                                    fluid />
                                <app-form-error [control]="creditMoneyForm.controls['network']" [controlName]="'Network'"
                                    [apiErrorType]="'network'" />
                            </div> -->
                            <div>
                                <label for="amount" class="required-label block font-bold mb-3">Amount</label>
                                <p-inputnumber formControlName="amount" aria-label="Amount" inputId="amount" 
                                    placeholder="Amount"  mode="decimal" [minFractionDigits]="2" required fluid />
                                <app-form-error [control]="creditMoneyForm.controls['amount']" [controlName]="'Amount'"
                                    [apiErrorType]="'amount'" />
                            </div>
                        </div>
                    </form>
                </div>
                <div class="flex justify-end gap-2 mt-6 border-t border-gray-200 pt-2">
                    <p-button label="Cancel" icon="pi pi-times" text (click)="close()" />
                    <p-button label="Save" icon="pi pi-check" (click)="saveCreditMoney()" />
                </div>
            `,
    providers: [DialogService],
})
export class CreditMoneyModal implements OnInit {
    private fb = inject(FormBuilder);
    private messageService = inject(MessageService);
    private ref = inject(DynamicDialogRef);
    private config = inject(DynamicDialogConfig);
    private loaderService = inject(LoaderService);
    private serverApiService = inject(ServerApiService);

    creditMoneyForm!: FormGroup;
    currencies = [{ id: "usd", name: "USD" }, { id: "gbp", name: "GBP" }, { id: "eur", name: "EUR" }];
    networks = [{ id: "ach", name: "ACH" }, { id: "fps", name: "FPS" }, { id: "rtp", name: "RTP" }, { id: "wire", name: "Wire" }];
    connectAccounts_id: any = null;
    financialAccount_id: any = null;
    financialAddress_id: any = null;

    ngOnInit() {
        this.connectAccounts_id = this.config.data.connectAccounts_id;
        this.financialAddress_id = this.config.data.financialAddress_id;
        this.financialAccount_id = this.config.data.financialAccount_id;
        this.createCreditMoneyForm();
    }

    createCreditMoneyForm() {
        this.creditMoneyForm = this.fb.group({
            currency: ['gbp', Validators.required],
            // network: ['ach', Validators.required],
            amount: [null, Validators.required],
        });
    }

    saveCreditMoney() {
        if (!this.creditMoneyForm.valid) {
            this.creditMoneyForm.markAllAsTouched();
            this.creditMoneyForm.updateValueAndValidity();
            this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Please fill all the required fields', life: 3000, });
            return;
        }
        this.loaderService.show();
        this.serverApiService.post(`connect/connect-accounts/${this.connectAccounts_id}/financial-accounts/${this.financialAccount_id}/addresses/${this.financialAddress_id}/credit`, this.creditMoneyForm.value).subscribe({
            next: (res: any) => { 
                this.ref.close(res);
                this.messageService.add({ severity: 'success', summary: 'Successful', detail: res.message, life: 3000, });
            },
            error: (err: any) => {
                this.loaderService.hide();
                this.messageService.add({ severity: 'error', summary: 'Error', detail: err.error.message, life: 3000, });
            },
            complete: () => { this.loaderService.hide(); },
        });
    }

    close() {
        this.ref.close();
    }
}
