import { Component, inject, OnInit, ViewChild } from '@angular/core';
import { Table, TableLazyLoadEvent, TablePageEvent } from 'primeng/table';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { ConfirmationService, MessageService } from 'primeng/api';
import { ActivatedRoute, Router } from '@angular/router';
import { ApiService } from '../../core/services/api.service';
import { LoaderService } from '../../core/services/loader.service';
import { debounceTime } from 'rxjs';
import { HttpParams } from '@angular/common/http';
import { createFormData, filterParams } from '../../core/utils/utils';
import { AddBusinessComponent } from './add-business/add-business.component';
import { PrimengModules } from '../../core/utils/primeng';
import { AuthService } from '../../core/services/authentication.service';

@Component({
  selector: 'app-business',
  standalone: true,
  imports: [PrimengModules, AddBusinessComponent],
  templateUrl: './business.component.html',
  styleUrl: './business.component.scss',
  providers: [ConfirmationService],
})
export class BusinessComponent implements OnInit {
  private confirmationService = inject(ConfirmationService);
  private messageService = inject(MessageService);
  private fb = inject(FormBuilder);
  private router = inject(Router);
  private apiService = inject(ApiService);
  public loaderService = inject(LoaderService);
  public authService = inject(AuthService);

  @ViewChild('dt') dt!: Table;

  filterForm!: FormGroup;
  search = new FormControl('');
  businesses: any[] = [];
  totalRecords: number = 0;
  selectedBusiness: any = null;
  isEdit = false;
  businessDialog = false;
  statusOptions: any[] = [
    { name: 'Active', value: 'active' },
    { name: 'Inactive', value: 'inactive' },
  ];


  constructor() {
    this.filterForm = this.fb.group({
      page: [],
      per_page: [],
      pagination: [true],
    });
  }

  ngOnInit() {
    this.search.valueChanges.pipe(debounceTime(400)).subscribe((val) => {
      this.fetchBusiness();
    });
  }

  fetchBusiness() {
    const httpParams: HttpParams = filterParams({ ...this.filterForm.value, search: this.search.value, });
    this.loaderService.show();
    this.apiService.get('businesses', httpParams).subscribe({
      next: (res: any) => { this.businesses = res.data; this.totalRecords = res.total; },
      error: (err: any) => { this.loaderService.hide(); },
      complete: () => { this.loaderService.hide(); },
    });
  }

  openDialogCreateBusiness() {
    this.businessDialog = true;
    this.isEdit = false;
    this.selectedBusiness = null;
  }

  openDialogEditBusiness(business: any) {
    this.businessDialog = true;
    this.selectedBusiness = business;
    this.isEdit = true;
  }

  handleSave(event: any) {
    this.businessDialog = false;
    this.isEdit = false;
    this.selectedBusiness = null;
    this.fetchBusiness();
  }

  viewBusiness(business: any) { this.router.navigate(['/business', business.id]); }

  deleteBusiness(business: any) {
    this.confirmationService.confirm({
      message: `Are you sure you want to delete ${business.name} ?`,
      header: 'Confirm',
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        this.loaderService.show();
        this.apiService.delete(`businesses/${business.id}`).subscribe({
          next: (res: any) => {
            this.messageService.add({ severity: 'success', summary: 'Successful', detail: `${business.name} deleted`, life: 3000, });
            this.fetchBusiness();
          },
          error: (err: any) => { this.loaderService.hide(); },
          complete: () => { this.loaderService.hide(); },
        });
      },
    });
  }

  toggleStatus(event: any, business: any) {
    business.disabled = event.checked ? true : false;
    this.loaderService.show();
    const url = business.disabled ? `businesses/${business.id}/disable` : `businesses/${business.id}/enable`;
    this.apiService.post(url, null).subscribe({
      next: (res: any) => {
        this.messageService.add({ severity: 'success', summary: 'Successful', detail: `${business.name} ${business.disabled ? 'disabled' : 'enabled'}`, life: 3000, });
      },
      error: (err: any) => { this.loaderService.hide(); },
      complete: () => { this.loaderService.hide(); },
    });
  }

  onLazyLoad(event: any) {
    if (
      event.first >= 0 &&
      event.rows &&
      (this.filterForm.value.page !== event.first / event.rows + 1 ||
        this.filterForm.value.per_page !== event.rows)
    ) {
      this.filterForm.controls['page'].setValue(event.first / event.rows + 1, { emitEvent: false });
      this.filterForm.controls['per_page'].setValue(event.rows, { emitEvent: false });
      this.fetchBusiness();
    }
    if (event.sortField && event.sortOrder) {
      this.businesses.sort((a, b) => {
        if (a[event.sortField] < b[event.sortField]) {
          return event.sortOrder === 1 ? -1 : 1;
        }
        if (a[event.sortField] > b[event.sortField]) {
          return event.sortOrder === 1 ? 1 : -1;
        }
        return 0;
      });
    }
  }
}
