import { Component, inject, OnInit, ViewChild } from '@angular/core';
import { Table, TableLazyLoadEvent, TablePageEvent } from 'primeng/table';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { ConfirmationService, MessageService } from 'primeng/api';
import { ActivatedRoute, Router } from '@angular/router';
import { ApiService } from '../../core/services/api.service';
import { LoaderService } from '../../core/services/loader.service';
import { debounceTime } from 'rxjs';
import { HttpParams } from '@angular/common/http';
import { createFormData, filterParams } from '../../core/utils/utils';
import { PrimengModules } from '../../core/utils/primeng';
import { CreateVendorComponent } from './create-vendor/create-vendor.component';
import { AuthService } from '../../core/services/authentication.service';


@Component({
  selector: 'app-vendor',
  imports: [PrimengModules, CreateVendorComponent],
  standalone: true,
  templateUrl: './vendor.component.html',
  styleUrl: './vendor.component.scss',
  providers: [ConfirmationService],
})
export class VendorComponent implements OnInit {
  private confirmationService = inject(ConfirmationService);
  private messageService = inject(MessageService);
  private fb = inject(FormBuilder);
  private router = inject(Router);
  private apiService = inject(ApiService);
  public loaderService = inject(LoaderService);
  public authService = inject(AuthService);

  filterForm!: FormGroup;
  search = new FormControl('');
  @ViewChild('dt') dt!: Table;
  vendors: any[] = [];
  totalRecords: number = 0;
  selectedVendor: any = null;
  isEdit = false;
  vendorDialog = false;
 
  constructor() {
    this.filterForm = this.fb.group({
      page: [],
      per_page: [],
      pagination: [true],
    });
  }

  ngOnInit() {
    this.search.valueChanges.pipe(debounceTime(400)).subscribe((val) => {
      this.fetchVendors();
    });
  }

  fetchVendors() {
    const httpParams: HttpParams = filterParams({
      ...this.filterForm.value,
      search: this.search.value,
    });
    this.loaderService.show();
    this.apiService.get('users', httpParams).subscribe({
      next: (res: any) => {
        this.vendors = res.data;
        this.totalRecords = res.total;
      },
      error: (err: any) => {
        this.loaderService.hide();
        this.vendors = [];
        this.totalRecords = 0;
      },
      complete: () => {
        this.loaderService.hide();
      },
    });
  }

  openDialogCreateVendor() {
    this.vendorDialog = true;
    this.isEdit = false;
    this.selectedVendor = null;
  }

  openDialogEditVendor(vendor: any) {
    this.vendorDialog = true;
    this.selectedVendor = vendor;
    this.isEdit = true;
  }

  handleSave(event: any) {
    this.vendorDialog = false;
    this.isEdit = false;
    this.selectedVendor = null;
    this.fetchVendors();
  }

  viewVendor(vendor: any) {
    this.router.navigate(['/view-vendor', vendor.id]);
  }

  deleteVendor(vendor: any) {
    this.confirmationService.confirm({
      message: 'Are you sure you want to delete ' + vendor.first_name + '?',
      header: 'Confirm',  
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        this.loaderService.show();
        this.apiService.delete(`users/${vendor.id}`).subscribe({
          next: (res: any) => {
            this.messageService.add({
              severity: 'success',
              summary: 'Successful',
              detail: `${vendor.first_name} deleted`,
              life: 3000,
            });
            this.fetchVendors();
          },
          error: (err: any) => {
            this.loaderService.hide();
          },
          complete: () => {
            this.loaderService.hide();
          },
        });
      },
    });
  }

  toggleStatus(event: any, vendor: any) {
    vendor.disabled = event.checked ? true : false;
    const FormData = createFormData(vendor);
    this.loaderService.show();
    this.apiService.post(`users/${vendor.id}`, FormData).subscribe({
      next: (res: any) => {
        this.messageService.add({
          severity: 'success',
          summary: 'Successful',
          detail: `${vendor.first_name} ${event.checked ? 'activated' : 'deactivated'}`,
          life: 3000,
        });
      },
      error: (err: any) => {
        this.loaderService.hide();
      },
      complete: () => {
        this.loaderService.hide();
      },
    });
  }

  onLazyLoad(event: any) {
    if (
      event.first >= 0 &&
      event.rows &&
      (this.filterForm.value.page !== event.first / event.rows + 1 ||
        this.filterForm.value.per_page !== event.rows)
    ) {
      this.filterForm.controls['page'].setValue(event.first / event.rows + 1, {
        emitEvent: false,
      });
      this.filterForm.controls['per_page'].setValue(event.rows, {
        emitEvent: false,
      });
      this.fetchVendors();
    }
    if (event.sortField && event.sortOrder) {
      this.vendors.sort((a, b) => {
        if (a[event.sortField] < b[event.sortField]) {
          return event.sortOrder === 1 ? -1 : 1;
        }
        if (a[event.sortField] > b[event.sortField]) {
          return event.sortOrder === 1 ? 1 : -1;
        }
        return 0;
      });
    }
  }
}


// {
//     "id": 3,
//     "first_name": "Harshad",
//     "last_name": "Chavda",
//     "image": null,
//     "parent_id": null,
//     "dob": "2025-09-05",
//     "contact_emails": null,
//     "contact_numbers": null,
//     "door_no": null,
//     "city": "Birmingham",
//     "state": "West Milands",
//     "postcode": "WS14FE",
//     "residential_address": "tane close 123",
//     "country_id": null,
//     "login_email": "<EMAIL>",
//     "reseller": false,
//     "commission_type": "percentage",
//     "commission": 0,
//     "disabled": false,
//     "created_at": "2025-09-15T07:32:01.000000Z",
//     "country": null,
//     "image_url": null,
//     "thumb_url": null
// }