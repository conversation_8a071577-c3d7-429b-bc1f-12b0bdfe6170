import { CommonModule } from '@angular/common';
import { Component, inject } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { Router, RouterModule } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { CheckboxModule } from 'primeng/checkbox';
import { InputTextModule } from 'primeng/inputtext';
import { PasswordModule } from 'primeng/password';
import { RippleModule } from 'primeng/ripple';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { ApiService } from '../../../core/services/api.service';
import { LoaderService } from '../../../core/services/loader.service';
import { AuthService } from '../../../core/services/authentication.service';
import { MessageService } from 'primeng/api';
import { ToastModule } from 'primeng/toast';
import { MessageModule } from 'primeng/message';
import { createFormData } from '../../../core/utils/utils';

@Component({
  selector: 'app-admin-login',
  standalone: true,
  imports: [
    ButtonModule,
    CheckboxModule,
    ProgressSpinnerModule,
    InputTextModule,
    PasswordModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    RouterModule,
    RippleModule,
    ToastModule,
    MessageModule,
  ],
  templateUrl: './admin-login.component.html',
  styleUrl: './admin-login.component.scss',
  providers: [],
})
export class AdminLoginComponent {
  private apiService = inject(ApiService);
  private fb = inject(FormBuilder);
  private _router = inject(Router);
  private loaderService = inject(LoaderService);
  private authService = inject(AuthService);
  private messageService = inject(MessageService);

  loginForm!: FormGroup;
  checked: boolean = false;

  constructor() {
    this.loginForm = this.fb.group({
      username: ['', [Validators.required]],
      password: ['', [Validators.required]],
      rememberMe: [false],
    });
  }

  onSubmit() {
    if (!this.loginForm.valid) {
      Object.values(this.loginForm.controls).forEach((control) =>
        control.markAsTouched()
      );
      return;
    }

    const payload = {
      ...this.loginForm.value,
    };
    delete payload.rememberMe;
    const FormData = createFormData(payload);

    this.loaderService.show();
    this.apiService.post('admin/login', FormData).subscribe({
      next: (res: any) => {
        this.authService.setCredentials(res);
        this.messageService.add({
          severity: 'success',
          summary: 'Successful',
          detail: 'Login Successful',
          life: 3000,
        });
        this._router.navigate(['/home']);
      },
      error: (err: any) => {
        this.loaderService.hide();
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: err.error.message,
          life: 3000,
        });
      },
      complete: () => {
        this.loaderService.hide();
      },
    });
  }
}
