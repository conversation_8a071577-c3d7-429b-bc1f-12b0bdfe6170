<p-drawer header="{{ isEdit ? 'Edit' : 'Add' }} Business" [(visible)]="businessDialog"
    (visibleChange)="businessDialogChange.emit($event)" [autoZIndex]="true" position="right"
    styleClass="!w-full md:!w-[50rem] lg:!w-[70rem]" [modal]="true" [dismissible]="false">
    <ng-template #content>
        <form [formGroup]="businessForm">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div>
                    <label for="name" class="required-label block font-bold mb-3">Business Name</label>
                    <input type="text" aria-label="Business Name" pInputText id="name" formControlName="name"
                        placeholder="Business Name" autofocus fluid />
                    <app-form-error [control]="businessForm.controls['name']" [controlName]="'First name'"
                        [apiErrorType]="'name'" />
                </div>
                <div>
                    <label for="trading_name" class="required-label block font-bold mb-3">Trading Name</label>
                    <input type="text" pInputText id="trading_name" formControlName="trading_name"
                        placeholder="Trading Name" autofocus fluid />
                    <app-form-error [control]="businessForm.controls['trading_name']" [controlName]="'Trading Name'"
                        [apiErrorType]="'trading_name'" />
                </div>
                <div>
                    <label for="type" class="required-label block font-bold mb-3">Business Type</label>
                    <p-select [options]="business_types" optionLabel="name" optionValue="id" [showClear]="true"
                        formControlName="type" placeholder="Select a business type" [appendTo]="'body'"
                        class="w-full" />
                    <app-form-error [control]="businessForm.controls['type']" [controlName]="'Business Type'"
                        [apiErrorType]="'type'" />
                </div>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div>
                    <label for="postcode" class="required-label block font-bold mb-3">Postcode</label>
                    <div class="flex flex-col md:flex-row gap-6">
                        <p-inputgroup>
                            <input type="text" pInputText id="postcode" formControlName="postcode"
                                placeholder="Postcode" autofocus fluid />
                            <p-button label="Lookup" icon="pi pi-search" iconPos="left" styleClass="p-button-info"
                                (click)="lookupPostcode()" />
                        </p-inputgroup>
                    </div>
                    <app-form-error [control]="businessForm.controls['postcode']" [controlName]="'Postcode'"
                        [apiErrorType]="'postcode'" />
                </div>
                <div>
                    <label for="door_no" class=" block font-bold mb-3">Door No</label>
                    <input type="text" pInputText id="door_no" formControlName="door_no" placeholder="Door No" autofocus
                        fluid />
                    <app-form-error [control]="businessForm.controls['door_no']" [controlName]="'Door No'"
                        [apiErrorType]="'door_no'" />
                </div>
                <div>
                    <label for="address" class="required-label block font-bold mb-3">Street</label>
                    <input type="text" pInputText id="address" formControlName="address" placeholder="Street" autofocus
                        fluid />
                    <app-form-error [control]="businessForm.controls['address']" [controlName]="'Address'"
                        [apiErrorType]="'address'" />
                </div>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div>
                    <label for="city" class="required-label block font-bold mb-3">City</label>
                    <input type="text" pInputText id="city" formControlName="city" placeholder="City" autofocus fluid />
                    <app-form-error [control]="businessForm.controls['city']" [controlName]="'City'"
                        [apiErrorType]="'city'" />
                </div>
                <div>
                    <label for="state" class="required-label block font-bold mb-3">State</label>
                    <input type="text" pInputText id="state" formControlName="state" placeholder="State" autofocus
                        fluid />
                    <app-form-error [control]="businessForm.controls['state']" [controlName]="'State'"
                        [apiErrorType]="'state'" />
                </div>
                <div>
                    <label for="country_id" class="block font-bold mb-3">Country</label>
                    <p-select [options]="countries" optionLabel="name" optionValue="id" [showClear]="true"
                        formControlName="country_id" placeholder="Select a country" [appendTo]="'body'"
                        [loading]="is_countries_loaded" class="w-full" />
                    <app-form-error [control]="businessForm.controls['country_id']" [controlName]="'Country'"
                        [apiErrorType]="'country_id'" />
                </div>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                    <label for="email" class="required-label block font-bold mb-3">Email</label>
                    <input type="text" pInputText id="email" formControlName="email" placeholder="Email" autofocus
                        fluid />
                    <app-form-error [control]="businessForm.controls['email']" [controlName]="'Email'"
                        [apiErrorType]="'email'" />
                </div>
                <div>
                    <label for="contact_numbers" class="block font-bold mb-3">Contact Numbers</label>
                    <input type="text" pInputText id="contact_numbers" formControlName="contact_numbers"
                        placeholder="Contact Numbers" autofocus fluid />
                    <app-form-error [control]="businessForm.controls['contact_numbers']"
                        [controlName]="'Contact Numbers'" [apiErrorType]="'contact_numbers'" />
                </div>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div>
                    <label for="joining_date" class="required-label block font-bold mb-3">Joining Date</label>
                    <p-datePicker formControlName="joining_date" [showIcon]="true" [appendTo]="'body'"
                        styleClass="w-full" dateFormat="dd-mm-yy" placeholder="dd-mm-yyyy" />
                    <app-form-error [control]="businessForm.controls['joining_date']" [controlName]="'Joining Date'"
                        [apiErrorType]="'joining_date'" />
                </div>
                <div>
                    <label for="domain" class="block font-bold mb-3">Domain</label>
                    <input type="text" pInputText id="domain" formControlName="domain" placeholder="Domain" autofocus
                        fluid />
                    <app-form-error [control]="businessForm.controls['domain']" [controlName]="'Domain'"
                        [apiErrorType]="'domain'" />
                </div>
                <div>
                    <label for="domain_valid_till" class="block font-bold mb-3">Domain Valid Till</label>
                    <p-datePicker formControlName="domain_valid_till" [showIcon]="true" [appendTo]="'body'"
                        styleClass="w-full" dateFormat="dd-mm-yy" placeholder="dd-mm-yyyy" />
                    <app-form-error [control]="businessForm.controls['domain_valid_till']"
                        [controlName]="'Domain Valid Till'" [apiErrorType]="'domain_valid_till'" />
                </div>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                    <label for="statement_description" class="block font-bold mb-3">Statement Description</label>
                    <textarea pInputTextarea id="statement_description" formControlName="statement_description"
                        placeholder="Statement Description" autofocus fluid></textarea>
                    <app-form-error [control]="businessForm.controls['statement_description']"
                        [controlName]="'Statement Description'" [apiErrorType]="'statement_description'" />
                </div>
                <div>
                    <label for="statement_descriptor" class="block font-bold mb-3">Statement Descriptor</label>
                    <textarea pInputTextarea id="statement_descriptor" formControlName="statement_descriptor"
                        placeholder="Statement Descriptor" autofocus fluid></textarea>
                    <app-form-error [control]="businessForm.controls['statement_descriptor']"
                        [controlName]="'Statement Descriptor'" [apiErrorType]="'statement_descriptor'" />
                </div>
            </div>
            <ng-container
                *ngIf="businessForm.controls['type'].value && businessForm.controls['type'].value !== 'Sole trader' && businessForm.controls['type'].value !== 'Partnership'">
                <div class="mt-4">
                    <div class="text-lg font-bold mb-2">Company Details</div>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div>
                        <label for="company_number" class="block font-bold mb-3">Company Number</label>
                        <div class="flex flex-col md:flex-row gap-6">
                            <p-inputgroup>
                                <input type="text" pInputText id="company_number" formControlName="company_number"
                                    placeholder="Company Number" autofocus fluid />
                                <p-button label="Lookup" icon="pi pi-search" iconPos="left" styleClass="p-button-info"
                                    (click)="lookupCompany()" />
                            </p-inputgroup>
                        </div>
                        <app-form-error [control]="businessForm.controls['company_number']"
                            [controlName]="'Company Number'" [apiErrorType]="'company_number'" />
                    </div>
                    <div>
                        <label for="company_name" class="block font-bold mb-3">Company Name</label>
                        <input type="text" pInputText id="company_name" formControlName="company_name"
                            placeholder="Company Name" autofocus fluid />
                        <app-form-error [control]="businessForm.controls['company_name']" [controlName]="'Company Name'"
                            [apiErrorType]="'company_name'" />
                    </div>
                    <div>
                        <label for="company_type" class="block font-bold mb-3">Company Type</label>
                        <input type="text" pInputText id="company_type" formControlName="company_type"
                            placeholder="Company Type" autofocus fluid />
                        <app-form-error [control]="businessForm.controls['company_type']" [controlName]="'Company Type'"
                            [apiErrorType]="'company_type'" />
                    </div>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                        <label for="company_address_line_1" class="block font-bold mb-3">Company Address Line
                            1</label>
                        <input type="text" pInputText id="company_address_line_1"
                            formControlName="company_address_line_1" placeholder="Company Address Line 1" autofocus
                            fluid />
                        <app-form-error [control]="businessForm.controls['company_address_line_1']"
                            [controlName]="'Company Address Line 1'" [apiErrorType]="'company_address_line_1'" />
                    </div>
                    <div>
                        <label for="company_address_line_2" class="block font-bold mb-3">Company Address Line
                            2</label>
                        <input type="text" pInputText id="company_address_line_2"
                            formControlName="company_address_line_2" placeholder="Company Address Line 2" autofocus
                            fluid />
                        <app-form-error [control]="businessForm.controls['company_address_line_2']"
                            [controlName]="'Company Address Line 2'" [apiErrorType]="'company_address_line_2'" />
                    </div>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div>
                        <label for="company_locality" class="block font-bold mb-3">Company Locality</label>
                        <input type="text" pInputText id="company_locality" formControlName="company_locality"
                            placeholder="Company Locality" autofocus fluid />
                        <app-form-error [control]="businessForm.controls['company_locality']"
                            [controlName]="'Company Locality'" [apiErrorType]="'company_locality'" />
                    </div>
                    <div>
                        <label for="company_postal_code" class="block font-bold mb-3">Company Postal Code</label>
                        <input type="text" pInputText id="company_postal_code" formControlName="company_postal_code"
                            placeholder="Company Postal Code" autofocus fluid />
                        <app-form-error [control]="businessForm.controls['company_postal_code']"
                            [controlName]="'Company Postal Code'" [apiErrorType]="'company_postal_code'" />
                    </div>
                    <div>
                        <label for="company_country" class="block font-bold mb-3">Company Country</label>
                        <input type="text" pInputText id="company_country" formControlName="company_country"
                            placeholder="Company Country" autofocus fluid />
                        <app-form-error [control]="businessForm.controls['company_country']"
                            [controlName]="'Company Country'" [apiErrorType]="'company_country'" />
                    </div>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                        <label for="company_region" class="block font-bold mb-3">Company Region</label>
                        <input type="text" pInputText id="company_region" formControlName="company_region"
                            placeholder="Company Region" autofocus fluid />
                        <app-form-error [control]="businessForm.controls['company_region']"
                            [controlName]="'Company Region'" [apiErrorType]="'company_region'" />
                    </div>
                    <div>
                        <label for="company_date_of_creation" class="block font-bold mb-3">Company Date of
                            Creation</label>
                        <p-datePicker formControlName="company_date_of_creation" [showIcon]="true" [appendTo]="'body'"
                            styleClass="w-full" dateFormat="dd-mm-yy" placeholder="dd-mm-yyyy" />
                        <app-form-error [control]="businessForm.controls['company_date_of_creation']"
                            [controlName]="'Company Date of Creation'" [apiErrorType]="'company_date_of_creation'" />
                    </div>
                </div>
            </ng-container>

            <div class="mt-4">
                <div class="text-lg font-bold mb-2">Commission Details
                </div>
            </div>
            <div formGroupName="business_commissions">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                        <label for="baas_in_fee_type" class="block font-bold mb-3">BAAS In Fee Type</label>
                        <p-inputgroup>
                            <p-inputnumber formControlName="baas_in_fee" aria-label="BAAS In Fee" inputId="baas_in_fee"
                                placeholder="BAAS In Fee" [useGrouping]="false" mode="decimal" [minFractionDigits]="2"
                                suffix="{{businessForm.controls['business_commissions'].get('baas_in_fee_type')?.value == 'percentage' ? '%' : ''}}"
                                autofocus fluid />
                            <p-inputgroup-addon>
                                <p-selectbutton [options]="fee_types" formControlName="baas_in_fee_type"
                                    severity="primary" optionLabel="label" optionValue="value"
                                    ariaLabelledBy="BAAS In Fee Type" fluid />
                            </p-inputgroup-addon>
                        </p-inputgroup>
                        <app-form-error
                            [control]="businessForm.controls['business_commissions'].get('baas_in_fee_type')"
                            [controlName]="'BAAS In Fee Type'" [apiErrorType]="'baas_in_fee_type'" />
                        <app-form-error [control]="businessForm.controls['business_commissions'].get('baas_in_fee')"
                            [controlName]="'BAAS In Fee'" [apiErrorType]="'baas_in_fee'" />
                    </div>
                    <div>
                        <label for="baas_out_fee_type" class="block font-bold mb-3">BAAS Out Fee Type</label>
                        <p-inputgroup>
                            <p-inputnumber formControlName="baas_out_fee" aria-label="BAAS Out Fee"
                                inputId="baas_out_fee" placeholder="BAAS Out Fee" [useGrouping]="false" mode="decimal"
                                [minFractionDigits]="2"
                                suffix="{{businessForm.controls['business_commissions'].get('baas_out_fee_type')?.value == 'percentage' ? '%' : ''}}"
                                autofocus fluid />
                            <p-inputgroup-addon>
                                <p-selectbutton [options]="fee_types" formControlName="baas_out_fee_type"
                                    severity="primary" optionLabel="label" optionValue="value"
                                    ariaLabelledBy="BAAS Out Fee Type" fluid />
                            </p-inputgroup-addon>
                        </p-inputgroup>

                        <app-form-error
                            [control]="businessForm.controls['business_commissions'].get('baas_out_fee_type')"
                            [controlName]="'BAAS Out Fee Type'" [apiErrorType]="'baas_out_fee_type'" />
                        <app-form-error [control]="businessForm.controls['business_commissions'].get('baas_out_fee')"
                            [controlName]="'BAAS Out Fee'" [apiErrorType]="'baas_out_fee'" />
                    </div>
                </div>
            </div>
        </form>
    </ng-template>
    <ng-template #footer>
        <div class="flex items-center gap-2">
            <button pButton label="Cancel" icon="pi pi-times" class="w-full" outlined (click)="closeDialog()"></button>
            <button pButton label="Save" icon="pi pi-check" class="w-full" (click)="saveBusiness()"></button>
        </div>
    </ng-template>

</p-drawer>