<div class="card flex flex-col gap-4">
    <div class="flex justify-between">
        <div class="font-semibold text-xl">

            <p-button icon="pi pi-arrow-left" (click)="router.navigate(['/business'])" text />
            {{ business?.name || '--' }}
        </div>
        <p-splitbutton label="Setting" icon="pi pi-cog" [model]="items" outlined severity="contrast" />
    </div>
    <div class="flex flex-wrap gap-2">
        @if (business?.status === 'active') {
        <p-button label="{{ business?.status | titlecase}}" severity="success" outlined />
        }
        <!-- <p-button label="" severity="secondary" outlined />
        <p-button label="Success" severity="success" outlined />
        <p-button label="Info" severity="info" outlined />
        <p-button label="warn" severity="warn" outlined />
        <p-button label="Help" severity="help" outlined />
        <p-button label="Danger" severity="danger" outlined />
        <p-button label="Contrast" severity="contrast" outlined /> -->
    </div>
    <p-tabs [(value)]="activeTab" scrollable [showNavigators]="true">
        <p-tablist>
            <p-tab [value]="0">Business Details</p-tab>
            @if (authService._superman || authService._permissions?.businesses?.connect_account?.view) {
            <p-tab [value]="1">Connect Accounts</p-tab>
            }
            @if (authService._superman || authService._permissions?.businesses?.transactions?.view) {
            <p-tab [value]="2">Transactions</p-tab>
            }
            @if (authService._superman || authService._permissions?.businesses?.receive_credit?.view) {
            <p-tab [value]="3">Receive Credits</p-tab>
            }
            <p-tab [value]="4">Business Fees/Charges</p-tab>
        </p-tablist>
        <p-tabpanels>
            <p-tabpanel [value]="0">
                @if (activeTab === 0) {
                <app-business-details [business]="business" (save)="handleSave($event)"></app-business-details>
                }
            </p-tabpanel>
            @if (authService._superman || authService._permissions?.businesses?.connect_account?.view) {
            <p-tabpanel [value]="1">
                @if (activeTab === 1) {
                <app-connect-account [business]="business"></app-connect-account>
                }
            </p-tabpanel>
            }
            @if (authService._superman || authService._permissions?.businesses?.transactions?.view) {
            <p-tabpanel [value]="2">
                @if (activeTab === 2) {
                <app-business-transactions [business]="business"></app-business-transactions>
                }
            </p-tabpanel>
            }
            @if (authService._superman || authService._permissions?.businesses?.receive_credit?.view) {
            <p-tabpanel [value]="3">
                @if (activeTab === 3) {
                <app-business-receive-credit [business]="business"></app-business-receive-credit>
                }
            </p-tabpanel>
            }
            <p-tabpanel [value]="4">
                @if (activeTab === 4) {
                <app-business-fees-charges [business]="business"></app-business-fees-charges>
                }
            </p-tabpanel>
        </p-tabpanels>
    </p-tabs>
</div>

@if(businessDialog){
<app-add-business [(businessDialog)]="businessDialog" (save)="handleSave($event)" [businessData]="business"
    [isEdit]="isEdit"></app-add-business>
}

<p-confirmdialog [style]="{ width: '450px' }" />