import { Component, inject, OnInit, ViewChild } from '@angular/core';
import { PrimengModules } from '../../../core/utils/primeng';
import { ApiService } from '../../../core/services/api.service';
import { LoaderService } from '../../../core/services/loader.service';
import { ServerApiService } from '../../../core/services/server-api.service';
import { AuthService } from '../../../core/services/authentication.service';
import { HttpParams } from '@angular/common/http';
import { dateToString, filterParams } from '../../../core/utils/utils';
import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';
import { ConfirmationService, MessageService } from 'primeng/api';
import { FormControl, Validators } from '@angular/forms';
import { Popover } from 'primeng/popover';
import { AddBankAccountModal } from '../../business/connect-account/connect-dialog/addBankAccountModal';
import { TransferOtherBusinessAccModal } from '../../business/connect-account/connect-dialog/transferOtherBusinessAcc';
import { FundTransferFromFinancialModal } from '../../business/connect-account/connect-dialog/fundTransferFromFinancial';
import { CreditMoneyModal } from '../../business/connect-account/connect-dialog/creditMoneyModal';
import { ShowBalancePopup } from '../../business/connect-account/connect-dialog/showBalancePopup';
import { FinancialConnectionModal } from '../../business/connect-account/connect-dialog/financialConnection';
import { AccountTransactionsComponent } from '../account-transactions/account-transactions.component';

@Component({
  selector: 'app-account-owner',
  imports: [PrimengModules, AccountTransactionsComponent],
  standalone: true,
  templateUrl: './account-owner.component.html',
  styleUrl: './account-owner.component.scss',
  providers: [ConfirmationService, DialogService],
})
export class AccountOwnerComponent implements OnInit {
  public loaderService = inject(LoaderService);
  private serverApiService = inject(ServerApiService);
  public authService = inject(AuthService);
  private messageService = inject(MessageService);
  private dialogService = inject(DialogService);
  private confirmationService = inject(ConfirmationService);
  ref: DynamicDialogRef | undefined;

  @ViewChild('downloadStatementOp') downloadStatementOp!: Popover;

  business: any = null;
  accountData: any = null;
  activeTab: number = 0; // default tab


  financial_accounts: any[] = [];
  bank_accounts: any[] = [];
  connectAccounts: any = null;
  loderData = Array.from({ length: 5 }).map((_, i) => `Item #${i}`);
  is_financial_accounts_loading = false;
  is_bank_accounts_loading = false;

  statementDateRange = new FormControl(null, [Validators.required]);

  constructor() { }
  ngOnInit() {
    this.fetchAccountData();
  }

  fetchAccountData() {
    this.loaderService.show();
    this.serverApiService.get('connect/connect-accounts/admin').subscribe({
      next: (res: any) => {
        this.business = res;
        this.accountData = res.stripe_account;
        this.connectAccounts = res;
        this.fetchFinancialConnections();
        this.fetchBankAccounts();
      },
      error: (err: any) => {
        this.loaderService.hide();
        this.messageService.add({ severity: 'error', summary: 'Error', detail: err.error.message, life: 3000, });
      },
      complete: () => {
        this.loaderService.hide();
      }
    });
  }

  getCapabilityStatus(capability: string): string {
    const status = this.accountData.capabilities[capability];
    switch (status) {
      case 'active': return 'success';
      case 'pending': return 'warn';
      case 'inactive': return 'danger';
      default: return 'secondary';
    }
  }

  getCountryName(code: string): string {
    const countries: { [key: string]: string } = {
      'GB': 'United Kingdom',
      'US': 'United States',
      'CA': 'Canada',
      'FR': 'France',
      'DE': 'Germany'
    };
    return countries[code] || code;
  }

  getActiveCapabilities(): number {
    if (!this.accountData?.capabilities) return 0;
    return Object.values(this.accountData.capabilities).filter(status => status === 'active').length;
  }

  getPendingCapabilities(): number {
    if (!this.accountData?.capabilities) return 0;
    return Object.values(this.accountData.capabilities).filter(status => status === 'pending').length;
  }

  getInactiveCapabilities(): number {
    if (!this.accountData?.capabilities) return 0;
    return Object.values(this.accountData.capabilities).filter(status => status === 'inactive').length;
  }

  formatCapabilityName(capability: string): string {
    return capability.replace(/_/g, ' ');
  }

  getCapabilityKeys(): string[] {
    if (!this.accountData?.capabilities) return [];
    return Object.keys(this.accountData.capabilities);
  }


  //// Handle Financial Account Start

  fetchFinancialConnections() {
    const httpParams: HttpParams = filterParams({ nopaginate: 1, is_admin: true });
    this.is_financial_accounts_loading = true;
    this.serverApiService.get(`connect/connect-accounts/${this.connectAccounts.id}/financial-accounts`, httpParams).subscribe({
      next: (res: any) => { this.financial_accounts = res; },
      error: (err: any) => { this.is_financial_accounts_loading = false; this.financial_accounts = []; },
      complete: () => { this.is_financial_accounts_loading = false; },
    });
  }

  fetchFinancialConnectionsHardRefresh() {
    const httpParams: HttpParams = filterParams({ nopaginate: 1 });
    this.is_financial_accounts_loading = true;
    this.serverApiService.get(`connect/connect-accounts/financial-accounts/admin-hard-refresh`, httpParams).subscribe({
      next: (res: any) => { this.financial_accounts = res; },
      error: (err: any) => { this.is_financial_accounts_loading = false; },
      complete: () => { this.is_financial_accounts_loading = false; },
    });
  }

  openDialogToAddFinancialConnection() {
    this.ref = this.dialogService.open(FinancialConnectionModal, {
      data: { connectAccounts_id: this.connectAccounts.id },
      header: 'Add Financial Connection',
      modal: true,
      dismissableMask: false,
      closable: true,
      showHeader: true,
      width: '30vw',
      contentStyle: { overflow: 'auto' },
      breakpoints: {
        '960px': '55vw',
        '640px': '90vw'
      },
    });

    this.ref.onClose.subscribe((data: any) => {
      if (data) {
        this.fetchFinancialConnections();
      }
    });
  }

  viewFinancialAccountBalance(financialAccount: any) {
    this.ref = this.dialogService.open(ShowBalancePopup, {
      data: {
        connectAccounts_id: this.connectAccounts.id,
        financialAccount_id: financialAccount.id,
      },
      header: `View Balance (${financialAccount?.metadata?.nickname})`,
      modal: true,
      dismissableMask: false,
      closable: true,
      showHeader: true,
      width: '40vw',
      contentStyle: { overflow: 'auto' },
      styleClass: 'bg-gray-100',
      breakpoints: {
        '960px': '55vw',
        '640px': '90vw'
      },
    });
  }

  openDialogToAddAddress(financialAccount: any) {
    this.is_financial_accounts_loading = true;
    this.serverApiService.post(`connect/connect-accounts/${this.connectAccounts.id}/financial-accounts/${financialAccount.id}/addresses`, { currency: financialAccount.holds_currencies }).subscribe({
      next: (res: any) => { this.fetchFinancialConnections(); },
      error: (err: any) => {
        this.is_financial_accounts_loading = false;
        this.messageService.add({ severity: 'error', summary: 'Error', detail: err.error.message, life: 3000, });
      },
      complete: () => { this.is_financial_accounts_loading = false; },
    });
  }

  OpenCreditMoneyModal(financialAccount: any) {
    this.ref = this.dialogService.open(CreditMoneyModal, {
      data: {
        connectAccounts_id: this.connectAccounts.id,
        financialAccount_id: financialAccount.id,
        financialAddress_id: financialAccount?.financial_addresses?.id,
      },
      header: `Credit Money (${financialAccount?.metadata?.nickname})`,
      modal: true,
      dismissableMask: false,
      closable: true,
      showHeader: true,
      width: '30vw',
      contentStyle: { overflow: 'auto' },
      breakpoints: {
        '960px': '55vw',
        '640px': '90vw'
      },
    });

    this.ref.onClose.subscribe((data: any) => {
      if (data) {
        this.fetchFinancialConnections();
      }
    });
  }

  fundTransferFromFinancialAccount(financialAccount: any) {
    this.ref = this.dialogService.open(FundTransferFromFinancialModal, {
      data: {
        connectAccounts_id: this.connectAccounts.id,
        financialAccount_id: financialAccount.id,
        bank_accounts: this.bank_accounts.filter((a: any) => !a.archived),
      },
      header: `Fund Transfer (${financialAccount?.metadata?.nickname})`,
      modal: true,
      dismissableMask: false,
      closable: true,
      showHeader: true,
      width: '30vw',
      contentStyle: { overflow: 'auto' },
      breakpoints: {
        '960px': '55vw',
        '640px': '90vw'
      },
    });
    this.ref.onClose.subscribe((data: any) => {
      if (data) {
        // this.fetchFinancialConnections();
      }
    });
  }

  deleteFinancialConnection(financialAccount: any) {
    this.confirmationService.confirm({
      message: 'Are you sure you want to delete ' + (financialAccount?.financial_addresses?.account_holder_name) + '?',
      header: 'Confirm',
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        this.is_financial_accounts_loading = true;
        this.serverApiService.delete(`connect/connect-accounts/${this.connectAccounts.id}/financial-accounts/${financialAccount.id}`).subscribe({
          next: (res: any) => { this.fetchFinancialConnections(); },
          error: (err: any) => {
            this.is_financial_accounts_loading = false;
            this.messageService.add({ severity: 'error', summary: 'Error', detail: err.error.message, life: 3000, });
          },
          complete: () => { this.is_financial_accounts_loading = false; },
        });

      },
    });
  }

  downloadStatement(financialAccount: any) {
    if (!this.statementDateRange.value) {
      this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Date range is required', life: 3000, });
      return;
    }
    this.downloadStatementOp.hide();
    const url = `${this.serverApiService.baseUrl}/connect/connect-accounts/${this.connectAccounts.id}/financial-accounts/${financialAccount.id}/statement?from_date=${dateToString(this.statementDateRange.value[0])}&to_date=${dateToString(this.statementDateRange.value[1])}`;
    window.open(url, '_blank');
  }

  transferBetweenOtherBusinessAccounts(financialAccount: any) {
    this.ref = this.dialogService.open(TransferOtherBusinessAccModal, {
      data: {
        connectAccounts_id: this.connectAccounts.id,
        financialAccount_id: financialAccount.id,
      },
      header: `Transfer Between Other Business Accounts (${financialAccount?.metadata?.nickname})`,
      modal: true,
      dismissableMask: false,
      closable: true,
      showHeader: true,
      width: '30vw',
      contentStyle: { overflow: 'auto' },
      breakpoints: {
        '960px': '55vw',
        '640px': '90vw'
      },
    });
  }
  //// Handle Financial Account End

  //// Handle Bank Account Start

  fetchBankAccounts() {
    const httpParams: HttpParams = filterParams({ nopaginate: 1, is_admin: true });
    this.is_bank_accounts_loading = true;
    this.serverApiService.get(`connect/connect-accounts/${this.connectAccounts.id}/bank-accounts`, httpParams).subscribe({
      next: (res: any) => { this.bank_accounts = res; },
      error: (err: any) => { this.is_bank_accounts_loading = false; this.bank_accounts = []; },
      complete: () => { this.is_bank_accounts_loading = false; },
    });
  }

  fetchBankAccountsHardRefresh() {
    const httpParams: HttpParams = filterParams({ nopaginate: 1 });
    this.is_bank_accounts_loading = true;
    this.serverApiService.get(`connect/connect-accounts/bank-accounts/admin-hard-refresh`, httpParams).subscribe({
      next: (res: any) => { this.bank_accounts = res; },
      error: (err: any) => { this.is_bank_accounts_loading = false; },
      complete: () => { this.is_bank_accounts_loading = false; },
    });
  }

  openDialogToAddBankAccount(bankAccount?: any) {
    this.ref = this.dialogService.open(AddBankAccountModal, {
      data: {
        connectAccounts_id: this.connectAccounts.id,
        bankAccount: bankAccount,
      },
      header: `${bankAccount ? 'Edit' : 'Add'} Bank Account`,
      modal: true,
      dismissableMask: false,
      closable: true,
      showHeader: true,
      width: '30vw',
      contentStyle: { overflow: 'auto' },
      breakpoints: {
        '960px': '55vw',
        '640px': '90vw'
      },
    });
    this.ref.onClose.subscribe((data: any) => {
      if (data) {
        this.fetchBankAccounts();
      }
    });
  }

  deleteBankAccount(bankAccount: any) {
    this.confirmationService.confirm({
      message: 'Are you sure you want to delete ' + bankAccount?.bank_name + '?',
      header: 'Confirm',
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        this.is_bank_accounts_loading = true;
        this.serverApiService.delete(`connect/connect-accounts/${this.connectAccounts.id}/bank-accounts/${bankAccount.id}`).subscribe({
          next: (res: any) => { this.fetchBankAccounts(); },
          error: (err: any) => { this.is_bank_accounts_loading = false; },
          complete: () => { this.is_bank_accounts_loading = false; },
        });

      },
    });
  }

  //// Handle Bank Account End

  getSeverity(status: string) {
    switch (status) {
      case 'active': return 'success';
      case 'archived': return 'info';
      case 'pending': return 'info';
      case 'failed': return 'danger';
      default: return 'info';
    }
  }
}
