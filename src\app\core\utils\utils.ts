import { HttpParams } from '@angular/common/http';
import { FormGroup } from '@angular/forms';
import { AbstractControl, ValidationErrors } from '@angular/forms';

function filterParams(options: { [key: string]: any }): HttpParams {
  const filteredOptions: { [key: string]: string } = {};

  for (const [key, value] of Object.entries(options)) {
    if (value != null && value !== '') {
      filteredOptions[key] = String(value);
    }
  }

  return new HttpParams({ fromObject: filteredOptions });
}


function createFormData(options: { [key: string]: any }): FormData {
  const formData = new FormData();

  for (const [key, value] of Object.entries(options)) {
    if (value != null && value !== '') {
      if (value instanceof File || (Array.isArray(value) && value.every(item => item instanceof File))) {
        if (Array.isArray(value)) {
          value.forEach((file, index) => {
            formData.append(`${key}[${index}]`, file, file.name);
          });
        } else {
          formData.append(key, value, value.name);
        }
      } else if (Array.isArray(value)) {
        value.forEach((item, index) => {
          if (typeof item === 'object') {
            formData.append(`${key}[${index}]`, JSON.stringify(item));
          } else {
            formData.append(`${key}[${index}]`, item);
          }
        });
      } else if (typeof value === 'object' && !(value instanceof Date)) {
        formData.append(key, JSON.stringify(value));
      } else if (value instanceof Date) {
        const formatted = dateToString(value);
        formData.append(key, formatted);
      }
      else {
        formData.append(key, String(value));
      }
    }
  }

  return formData;
}


function dateToString(date: Date): string {
  const y = date.getFullYear();
  const m = String(date.getMonth() + 1).padStart(2, '0');
  const d = String(date.getDate()).padStart(2, '0');
  return `${y}-${m}-${d}`;
}

function checkBothPasswords(passwordKey: string, passwordConfirmationKey: string) {
  return (group: FormGroup) => {
    let passwordInput = group.controls[passwordKey],
      passwordConfirmationInput = group.controls[passwordConfirmationKey];
    if (passwordInput.value !== passwordConfirmationInput.value) {
      return passwordConfirmationInput.setErrors({ notEquivalent: true })
    } else if (passwordConfirmationInput.value === '') {
      return passwordConfirmationInput.setErrors({ required: true })
    } else {
      return passwordConfirmationInput.setErrors(null);
    }
  }
}

function ageValidator(genderKey: string, dateOfBirthKey: string) {
  return (group: FormGroup) => {
    let genderControl = group.controls[genderKey]
    let dobControl = group.controls[dateOfBirthKey]

    const dob = dobControl?.value;
    const gender = genderControl?.value;

    if (!dob || !gender) return null;

    const birthDate = new Date(dob);
    const today = new Date();

    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    const dayDiff = today.getDate() - birthDate.getDate();

    if (monthDiff < 0 || (monthDiff === 0 && dayDiff < 0)) {
      age--;
    }

    const isInvalid =
      (gender === 'male' && age < 21) || (gender === 'female' && age < 18);

    if (isInvalid) {
      dobControl.setErrors({ ageInvalid: true });
    } else {
      dobControl.setErrors(null);
    }

    return null;
  };
}


function minimumAgeValidator(minAge: number) {
  return (control: any) => {
    if (!control.value) return null;
    const today = new Date();
    const birthDate = new Date(control.value);
    let age = today.getFullYear() - birthDate.getFullYear();
    const m = today.getMonth() - birthDate.getMonth();
    if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age >= minAge ? null : { ageInvalid: true };
  };
}

function urlValidator(control: any) {
  if (!control.value) return null; // allow empty if not required
  const urlPattern = /^(https?:\/\/)?([\w\-]+\.)+[\w]{2,}(\/\S*)?$/;
  return urlPattern.test(control.value) ? null : { invalidUrl: true };
}

export { filterParams, createFormData, checkBothPasswords, ageValidator, minimumAgeValidator, urlValidator, dateToString };