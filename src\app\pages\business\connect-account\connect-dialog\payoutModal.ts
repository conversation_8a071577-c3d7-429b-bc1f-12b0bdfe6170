import { Component, OnInit, inject } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MessageService } from 'primeng/api';
import { DialogService, DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { PrimengModules } from '../../../../core/utils/primeng';
import { LoaderService } from '../../../../core/services/loader.service';
import { ServerApiService } from '../../../../core/services/server-api.service';
import { createFormData, filterParams } from '../../../../core/utils/utils';
import { HttpParams } from '@angular/common/http';

@Component({
    selector: 'app-payout-modal',
    imports: [PrimengModules],
    template: `
                    <div class="w-full">
                        <form [formGroup]="payoutForm" (ngSubmit)="savePayout()">
                            <div class="grid grid-cols-1 md:grid-cols-1 gap-4">
                                <div>   
                                    <label for="amount" class="required-label block font-bold mb-3">Amount</label>
                                    <p-inputnumber formControlName="amount" aria-label="Amount" inputId="amount"    
                                        placeholder="Amount"  mode="decimal" [minFractionDigits]="2" required fluid />
                                    <app-form-error [control]="payoutForm.controls['amount']" [controlName]="'Amount'"
                                        [apiErrorType]="'amount'" />
                                </div>
                            </div>
                            <!-- Bank Details -->
                             <div class="mt-4">
                                <div class="text-lg mb-2">Bank Details</div>
                            </div>
                             <div class="grid grid-cols-1 md:grid-cols-1 gap-4">
                                <div>   
                                    <label for="bank_account_id" class="required-label block font-bold mb-3">Bank Account</label>
                                    <p-select [options]="bank_accounts" optionValue="s_bank_account_id" [showClear]="true"
                                        formControlName="s_bank_account_id" placeholder="Select a bank account" [appendTo]="'body'" required
                                        fluid>
                                        <ng-template #selectedItem let-selectedOption>
                                            <div class="flex items-center gap-2">
                                                <div>{{ selectedOption.account_holder_name | titlecase}} - XXXX-XXXX-XXXX-{{selectedOption.account_number}}</div>
                                            </div>
                                        </ng-template>
                                        <ng-template let-option pTemplate="item">
                                            {{option.account_holder_name | titlecase}} - XXXX-XXXX-XXXX-{{option.account_number}}
                                        </ng-template>
                                    </p-select>
                                </div>
                                <!-- description -->
                                 <div>   
                                    <label for="description" class="required-label block font-bold mb-3">Description</label>
                                    <input type="text" aria-label="Description" pInputText id="description"
                                        formControlName="description" placeholder="Description" required autofocus fluid />
                                </div>
                                <!-- statement_descriptor -->
                                 <div>   
                                    <label for="statement_descriptor" class="block font-bold mb-3">Statement Descriptor</label>
                                    <input type="text" aria-label="Statement Descriptor" pInputText id="statement_descriptor"
                                        formControlName="statement_descriptor" placeholder="Statement Descriptor" autofocus fluid />
                                </div>
                            </div>
                            <button type="submit" style="display: none"></button>
                        </form>
                    </div>
                    <div class="flex justify-end gap-2 mt-6 border-t border-gray-200 pt-2">
                        <p-button label="Cancel" icon="pi pi-times" text (click)="close()" />
                                <p-button label="Save" icon="pi pi-check" (click)="savePayout()" />
                    </div>
            `,
    providers: [DialogService],
})
export class PayoutModal implements OnInit {
    private fb = inject(FormBuilder);
    private messageService = inject(MessageService);
    private ref = inject(DynamicDialogRef);
    private config = inject(DynamicDialogConfig);
    private loaderService = inject(LoaderService);
    private serverApiService = inject(ServerApiService);

    payoutForm!: FormGroup;
    connectAccounts: any = null;
    bank_accounts: any[] = [];

    constructor() {
        this.connectAccounts = this.config.data.connectAccounts;
        this.bank_accounts = this.config.data.connectAccounts?.external_accounts;
    }

    ngOnInit() {
        this.createPayoutForm();
    }

    createPayoutForm() {
        this.payoutForm = this.fb.group({
            amount: [null, Validators.required],
            s_bank_account_id: ['', Validators.required],
            description: ['', Validators.required],
            statement_descriptor: [''],
        });

        this.payoutForm.get('s_bank_account_id')?.valueChanges.subscribe((val) => {
            if (val) {
                const bankAccount = this.bank_accounts.find((b: any) => b.s_bank_account_id === val)
                if (bankAccount.description) {
                    this.payoutForm.get('description')?.setValue(bankAccount.description);
                } else {
                    this.payoutForm.get('description')?.setValue('ubsidifinancial.com');
                }

                if (bankAccount.statement_descriptor) {
                    this.payoutForm.get('statement_descriptor')?.setValue(bankAccount.statement_descriptor);
                } else {
                    this.payoutForm.get('statement_descriptor')?.setValue('ubsidifinancial.com');
                }
            }
        });
        this.payoutForm.get('s_bank_account_id')?.setValue(this.bank_accounts[0]?.s_bank_account_id);

    }

    savePayout() {
        if (!this.payoutForm.valid) {
            this.payoutForm.markAllAsTouched();
            this.payoutForm.updateValueAndValidity();
            this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Please fill all the required fields', life: 3000, });
            return;
        }
        const formData = createFormData(this.payoutForm.value);
        this.loaderService.show();
        this.serverApiService.post(`connect/connect-accounts/${this.connectAccounts.id}/payout`, formData).subscribe({
            next: (res: any) => { this.ref.close(res); },
            error: (err: any) => {
                this.loaderService.hide();
                this.messageService.add({ severity: 'error', summary: 'Error', detail: err.error.message, life: 3000, });
            },
            complete: () => { this.loaderService.hide(); },
        });
    }

    close() {
        this.ref.close();
    }
}
