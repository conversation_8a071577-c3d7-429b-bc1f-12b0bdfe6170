import { Component, inject } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { LoaderService } from './core/services/loader.service';
import { Toast } from "primeng/toast";

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [RouterOutlet, ProgressSpinnerModule, Toast],
  template: `
    @if (isLoading) {
      <div class="global-loader">
        <div class="whirly-loader"> </div>
      </div>
    <!-- <div class="loader-overlay">
      <p-progress-spinner strokeWidth="8" fill="transparent" animationDuration="1s"
        [style]="{ width: '60px', height: '60px' }"></p-progress-spinner>
    </div> -->
    }
    <router-outlet></router-outlet>
    <p-toast /> 
  `,
  styles: [
    `
      .loader-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(255, 255, 255, 0.8);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        transition: opacity 0.3s ease;
      }

      .loader-overlay.hidden {
        opacity: 0;
        pointer-events: none;
      }
    `,
  ],
})
export class AppComponent {
  private loaderService = inject(LoaderService);
  isLoading = true;

  constructor() {
    this.loaderService.loading$.subscribe((loading) => {
      this.isLoading = loading;
    });
    //  this.isLoading = true;
    // setTimeout(() => {
    //   this.isLoading = false;
    // }, 2000); // 2 seconds
  }
}
