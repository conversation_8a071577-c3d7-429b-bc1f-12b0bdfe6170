import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { AuthService } from '../services/authentication.service';

export const notAuthGuard: CanActivateFn = (route, state):boolean => {
  const authService = inject(AuthService);
  const router = inject(Router);

  if (!authService.isAdmin()) {
    return true;
  }

  router.navigate(['/home']);
  return false;
};



// not-auth.guard.ts
// import { inject } from '@angular/core';
// import { CanActivateChildFn, Router } from '@angular/router';
// import { AuthService } from '../services/authentication.service';
// import { map, take } from 'rxjs/operators';
// import { Observable, of } from 'rxjs';
// import { PLATFORM_ID } from '@angular/core';
// import { isPlatformBrowser } from '@angular/common';

// export const notAuthGuard: CanActivateChildFn = (childRoute, state): Observable<boolean> => {
//   const authService = inject(AuthService);
//   const router = inject(Router);
//   const platformId = inject(PLATFORM_ID);

//   return authService.isAuthenticated().pipe(
//     take(1),
//     map(isAuthenticated => {
//       if (isPlatformBrowser(platformId)) {
//         // Only perform navigation in the browser
//         if (isAuthenticated) {
//           router.navigate(['/dashboard']);
//           return false;
//         }
//       }
//       return !isAuthenticated; // Return the authentication status
//     })
//   );
// };
