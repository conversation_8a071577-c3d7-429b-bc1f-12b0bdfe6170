import { Component, EventEmitter, inject, Input, OnChanges, OnInit, Output } from '@angular/core';
import { PrimengModules } from '../../../../core/utils/primeng';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ApiService } from '../../../../core/services/api.service';
import { MessageService } from 'primeng/api';
import { createFormData } from '../../../../core/utils/utils';
import { createCompanyGroup, createConnectAccountForm, createExternalAccountGroup, createPersonGroup } from '../../../../core/model/connect-account.formGrop';
import { HttpParams } from '@angular/common/http';
import { LoaderService } from '../../../../core/services/loader.service';
import { ServerApiService } from '../../../../core/services/server-api.service';

@Component({
  selector: 'app-create-connect-account',
  imports: [PrimengModules],
  standalone: true,
  templateUrl: './create-connect-account.component.html',
  styleUrl: './create-connect-account.component.scss',
  providers: [],
})
export class CreateConnectAccountComponent implements OnInit, OnChanges {
  private apiService = inject(ApiService);
  private fb = inject(FormBuilder);
  private messageService = inject(MessageService);
  private loaderService = inject(LoaderService);
  private serverApiService = inject(ServerApiService);

  @Input() business: any = null;
  @Input() connectAccount: any = null;
  @Input() connectAccountDialog: boolean = false;
  @Output() connectAccountDialogChange = new EventEmitter<boolean>();
  @Output() save = new EventEmitter<any>();
  @Input() isEdit: boolean = false;

  connectAccountForm: FormGroup = createConnectAccountForm(this.fb);
  company: FormGroup = createCompanyGroup(this.fb);
  account_opener: FormGroup = createPersonGroup(this.fb);
  account_owner: FormGroup = createPersonGroup(this.fb);
  external_account: FormGroup = createExternalAccountGroup(this.fb);

  account_types = [{ id: "Default", name: "Default" }, { id: "Other", name: "Other" }];
  business_types = [{ id: "individual", name: "individual" }, { id: "company", name: "company" }];
  mcc_codes = [{ value: "5814", code: "", name: "Fast Food Restaurants" }, { value: "5999", code: "", name: "Miscellaneous Specialty Retail" }]
  countries: any[] = [];
  //18 years old
  maxDate = new Date(new Date().setFullYear(new Date().getFullYear() - 18));
  constructor() {
    this.fetchCountries();
  }

  ngOnInit() { }

  ngOnChanges() {
    if (this.isEdit && this.connectAccount) {
      this.connectAccountForm.patchValue({ ...this.connectAccount });
      this.company.patchValue({
        ...this.connectAccount.company,
        fiscal_year_end: new Date(this.connectAccount.company.fiscal_year_end),
      });
      this.account_opener.patchValue({
        ...this.connectAccount.account_opener,
        dob: new Date(this.connectAccount.account_opener.dob),
      });
      this.external_account.patchValue({
        ...this.connectAccount.external_accounts[0],
        country: this.business.country.iso,
        currency: this.business.country.currency_code,
        business_id: this.business.id,
      });
      if (this.connectAccount.account_owner) {
        this.account_owner.patchValue(this.connectAccount.account_owner);
      }
    } else {
      this.initConnectAccountForm();
    }
  }

  fetchCountries() {
    this.loaderService.show();
    this.apiService.get('countries/mini').subscribe({
      next: (res: any) => { this.countries = res; },
      error: (err: any) => { this.loaderService.hide(); },
      complete: () => { this.loaderService.hide(); },
    });
  }

  initConnectAccountForm() {
    if (!this.connectAccount) {
      this.connectAccountForm.patchValue({
        business_id: this.business.id,
        country: this.business.country.iso,
      });
      let address = `${this.business.door_no} ${this.business.address}`;
      this.company.patchValue({
        name: this.business.name,
        email: this.business.email,
        url: this.business.domain,
        line1: address,
        city: this.business.city,
        state: this.business.state,
        postal_code: this.business.postcode,
        phone_number: this.business.contact_numbers?.split(",")[0],
        fiscal_year_end: new Date(new Date().setFullYear(new Date().getFullYear() - 1)),
      });
      this.external_account.patchValue({
        country: this.business.country.iso,
        currency: this.business.country.currency_code,
        business_id: this.business.id,
      });
    }
  }

  saveConnectAccount() {
    if (this.checkValidForm()) {
      this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Please fill all the required fields', life: 3000, });
      return;
    }
    let payload = {
      ...this.connectAccountForm.value,
      company: this.company.value,
      account_opener: this.account_opener.value,
      external_account: this.external_account.value,
    };
    if (this.connectAccountForm.value.business_type === 'company') {
      this.account_owner.patchValue({ owner: true, account_opener: false });
      payload['account_owner'] = this.account_owner.value;
    }
    payload['account_opener']['account_opener'] = true;
    payload['external_account']['currency'] = this.countries.find((c: any) => c.iso === payload['external_account']['country'])?.currency_code;
    const FormData = createFormData(payload);
    this.loaderService.show();
    this.serverApiService.post('connect/connect-accounts', FormData).subscribe({
      next: (res: any) => {
        this.connectAccountDialog = false;
        this.connectAccountDialogChange.emit(false);
        this.save.emit(res.data);
        this.messageService.add({ severity: 'success', summary: 'Successful', detail: `${this.isEdit ? 'Updated' : 'Added'} Connect Account`, life: 3000, });
      },
      error: (err: any) => {
        this.loaderService.hide();
        this.messageService.add({ severity: 'error', summary: 'Error', detail: err.error.message, life: 3000, });
      },
      complete: () => { this.loaderService.hide(); },
    });
  }

  checkValidForm() {
    let isInvalid = false;
    if (!this.connectAccountForm.valid) { this.connectAccountForm.markAllAsTouched(); isInvalid = true; }
    if (!this.company.valid) { this.company.markAllAsTouched(); isInvalid = true; }
    if (!this.account_opener.valid) { this.account_opener.markAllAsTouched(); isInvalid = true; }
    if (!this.external_account.valid) { this.external_account.markAllAsTouched(); isInvalid = true; }
    if (this.connectAccountForm.value.business_type === 'company' && !this.account_owner.valid) {
      this.account_owner.markAllAsTouched();
      isInvalid = true;
    }
    return isInvalid;
  }

  closeDialog() {
    this.connectAccountDialog = false;
    this.connectAccountDialogChange.emit(false);
  }

  onFileSelected(event: Event, key: string) {
    const file = (event.target as HTMLInputElement).files?.[0];
    if (file) {
      this.connectAccountForm.controls[key].setValue(file);
      const reader = new FileReader();
      reader.onload = () => {
        // this.vendorData.image_url = reader.result as string; // preview image
      };
      reader.readAsDataURL(file);
    }
  }
}
