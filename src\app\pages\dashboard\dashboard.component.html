<div class="grid grid-cols-12 gap-8">
  <div class="col-span-12 lg:col-span-6 xl:col-span-3">
    <div class="card mb-0">
      <div class="flex justify-between mb-4">
        <div>
          <span class="block text-muted-color font-medium mb-4">Businesses</span>
          <div class="font-medium text-xl">{{ dashboard?.businesses }}</div>
        </div>
        <div class="flex items-center justify-center bg-purple-100  rounded-border"
          style="width: 2.5rem; height: 2.5rem">
          <i class="pi pi-briefcase text-purple-500 !text-xl"></i>
        </div>
      </div>
    </div>
  </div>
  <div class="col-span-12 lg:col-span-6 xl:col-span-3">
    <div class="card mb-0">
      <div class="flex justify-between mb-4">
        <div>
          <span class="block text-muted-color font-medium mb-4">Vendors</span>
          <div class="font-medium text-xl">{{ dashboard?.users }}</div>
        </div>
        <div class="flex items-center justify-center bg-cyan-100  rounded-border" style="width: 2.5rem; height: 2.5rem">
          <i class="pi pi-users text-cyan-500 !text-xl"></i>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="grid grid-cols-12 gap-8 mt-8">

  <div class="col-span-12 xl:col-span-6">
    <div class="card !mb-8">
      <div class="font-semibold text-xl mb-4">Recent Transactions</div>
      <p-table [value]="dashboard?.bank_transactions" [rows]="5" responsiveLayout="scroll">
        <ng-template #header>
          <tr>
            <th>Amount</th>
            <th>Category</th>
            <th pSortableColumn="created">Created<p-sortIcon field="created" /></th>
            <th>Status</th>
          </tr>
        </ng-template>
        <ng-template #body let-transaction>
          <tr>
            <td [ngClass]="{'text-green-500': transaction?.amount > 0, 'text-red-500': transaction?.amount < 0}">
              {{
              (transaction?.amount/100) | currency:'GBP':'symbol':'1.2-2' }}</td>
            <td [ngClass]="getCategoryColor(transaction?.category)">
              {{ transaction?.category.split('_').join(' ') | titlecase }}
            </td>
            <td>{{ transaction?.created | date:'dd MMM yyyy' }}</td>
            <td>
              <p-tag [severity]="getSeverity(transaction?.status)" value="{{ transaction?.status | titlecase }}" />
            </td>
          </tr>
        </ng-template>
        <ng-template #emptymessage>
          <tr>
            <td colspan="4" style="text-align: center !important;">No records found</td>
          </tr>
        </ng-template>
      </p-table>
    </div>
  </div>
  <div class="col-span-12 xl:col-span-6">
    <div class="card !mb-8">
      <div class="font-semibold text-xl mb-4">Recent Receive Credits</div>
      <p-table [value]="dashboard?.receive_credits" [rows]="5" responsiveLayout="scroll">
        <ng-template #header>
          <tr>
            <th>Business Name</th>
            <th>Amount</th>
            <th pSortableColumn="created"> Created<p-sortIcon field="created" /></th>
            <th>Status</th>
          </tr>
        </ng-template>
        <ng-template #body let-receiveCredit>
          <tr>
            <td>{{ receiveCredit?.business_name }}</td>
            <td [ngClass]="{
                            'text-green-500': receiveCredit?.amount > 0, 
                            'text-red-500': receiveCredit?.amount < 0
                            }">
              {{ (receiveCredit?.amount/100) | currency:'GBP':'symbol':'1.2-2' }}
            </td>
            <td>{{ receiveCredit?.created | date:'dd MMM yyyy' }}</td>
            <td>
              <p-tag [severity]="getSeverity(receiveCredit?.status)" value="{{ receiveCredit?.status | titlecase }}" />
            </td>
          </tr>

        </ng-template>
        <ng-template #emptymessage>
          <tr>
            <td colspan="4" style="text-align: center !important;">No records found</td>
          </tr>
        </ng-template>
      </p-table>
    </div>
  </div>
</div>