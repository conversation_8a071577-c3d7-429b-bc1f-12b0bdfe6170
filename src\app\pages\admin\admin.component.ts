import { Component, inject, OnInit, ViewChild } from '@angular/core';
import { Table, TableLazyLoadEvent, TablePageEvent } from 'primeng/table';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { ConfirmationService, MessageService } from 'primeng/api';
import { ActivatedRoute, Router } from '@angular/router';
import { ApiService } from '../../core/services/api.service';
import { LoaderService } from '../../core/services/loader.service';
import { debounceTime } from 'rxjs';
import { HttpParams } from '@angular/common/http';
import { createFormData, filterParams } from '../../core/utils/utils';
import { PrimengModules } from '../../core/utils/primeng';
import { PermissionsComponent } from './permissions/permissions.component';
import { AuthService } from '../../core/services/authentication.service';

@Component({
  selector: 'app-admin',
  imports: [PrimengModules, PermissionsComponent],
  templateUrl: './admin.component.html',
  styleUrl: './admin.component.scss',
  standalone: true,
  providers: [ConfirmationService],
})
export class AdminComponent implements OnInit {
  private apiService = inject(ApiService);
  private loaderService = inject(LoaderService);
  private fb = inject(FormBuilder);
  private messageService = inject(MessageService);
  private confirmationService = inject(ConfirmationService);
  public authService = inject(AuthService);

  filterForm!: FormGroup;
  adminForm!: FormGroup;
  search = new FormControl('');
  @ViewChild('dt') dt!: Table;
  admins: any[] = [];
  totalRecords: number = 0;
  adminDialog = false;
  selectedAdminId: any = null;
  roles: any[] = [];
  constructor() {

    this.createAdminForm();
    this.fetchRoles();
    this.filterForm = this.fb.group({
      page: [],
      limit: [],
    });
  }

  ngOnInit() {
    this.search.valueChanges.pipe(debounceTime(400)).subscribe((val) => {
      this.fetchAdmins();
    });
  }

  fetchRoles() {
    this.loaderService.show();
    this.apiService.get('role').subscribe({
      next: (res: any) => {
        this.roles = res;
      },
      error: (err: any) => {
        this.loaderService.hide();
      },
      complete: () => {
        this.loaderService.hide();
      },
    });
  }

  fetchAdmins() {
    const httpParams: HttpParams = filterParams({
      ...this.filterForm.value,
      query: this.search.value,
    });
    this.loaderService.show();
    this.apiService.get('admin', httpParams).subscribe({
      next: (res: any) => {
        this.admins = res.data.admins;
        this.totalRecords = res.data.totalItems;
      },
      error: (err: any) => {
        this.admins = [];
        this.totalRecords = 0;
        this.loaderService.hide();
      },
      complete: () => {
        this.loaderService.hide();
      },
    });
  }

  openDialogCreateAdmin() {
    this.adminDialog = true;
    this.createAdminForm();
  }

  openDialogEditAdmin(admin: any) {
    this.adminDialog = true;
    this.createAdminForm();
    this.adminForm.controls['password'].setValidators(null);
    this.adminForm.controls['password'].disable();
    admin.permissions = JSON.parse(admin.permissions);
    this.adminForm.patchValue(admin, { emitEvent: false });
    this.selectedAdminId = admin.id;
  }

  createAdminForm() {
    this.adminForm = this.fb.group({
      name: ['', Validators.required],
      role: ['', Validators.required],
      email: ['', [Validators.required, Validators.email]],
      mobile_number: [''],
      username: ['', Validators.required],
      password: ['', Validators.required],
      superman: [false],
      disabled: [false],
      image: [''],
      image_url: [''],
      permissions: [''],
      thumb_url: [''],
    });

    this.adminForm.get('role')?.valueChanges.subscribe((val) => {
      if (val && !this.adminForm.value.id) {
        const role = this.roles.find((role) => role.title === val);
        this.adminForm.controls['permissions'].setValue(JSON.parse(role.permissions));
      }
    });
  }

  handleSave(event: any) {
    this.adminDialog = false;
    this.adminForm.reset();
    this.fetchAdmins();
  }

  deleteAdmin(admin: any) {
    this.confirmationService.confirm({
      message: 'Are you sure you want to delete ' + admin.name + '?',
      header: 'Confirm',
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        this.loaderService.show();
        this.apiService.delete(`admin/${admin.id}`).subscribe({
          next: (res: any) => {
            this.messageService.add({
              severity: 'success',
              summary: 'Successful',
              detail: `${admin.name} deleted`,
              life: 3000,
            });
            this.fetchAdmins();
          },
          error: (err: any) => {
            this.loaderService.hide();
          },
          complete: () => {
            this.loaderService.hide();
          },
        });
      },
    });
  }

  toggleStatus(event: any, admin: any) {
    admin.disabled = event.checked ? true : false;
    admin.permissions = admin.permissions ? admin.permissions : { test: true };
    const adminId = admin.id;
    delete admin.id;
    const FormData = createFormData(admin);
    this.loaderService.show();
    this.apiService.put(`admin/${adminId}`, FormData).subscribe({
      next: (res: any) => {
        this.messageService.add({
          severity: 'success',
          summary: 'Successful',
          detail: `${admin.name} ${event.checked ? 'activated' : 'deactivated'}`,
          life: 3000,
        });
      },
      error: (err: any) => {
        this.loaderService.hide();
      },
      complete: () => {
        this.loaderService.hide();
      },
    });
  }

  onLazyLoad(event: any) {
    if (
      event.first >= 0 &&
      event.rows &&
      (this.filterForm.value.page !== event.first / event.rows + 1 ||
        this.filterForm.value.limit !== event.rows)
    ) {
      this.filterForm.controls['page'].setValue(event.first / event.rows + 1, {
        emitEvent: false,
      });
      this.filterForm.controls['limit'].setValue(event.rows, {
        emitEvent: false,
      });
      this.fetchAdmins();
    }
    if (event.sortField && event.sortOrder) {
      this.admins.sort((a, b) => {
        if (a[event.sortField] < b[event.sortField]) {
          return event.sortOrder === 1 ? -1 : 1;
        }
        if (a[event.sortField] > b[event.sortField]) {
          return event.sortOrder === 1 ? 1 : -1;
        }
        return 0;
      });
    }
  }

  closeDialog() {
    this.adminDialog = false;
    this.adminForm.reset();
  }

  saveAdmin() {
    if (!this.adminForm.valid) {
      Object.values(this.adminForm.controls).forEach((control) => {
        control.markAsTouched();
        control.updateValueAndValidity();
      });
      return;
    }
    // check permissions
    if (!this.adminForm.value.permissions) {
      this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Please select at least one permission', life: 3000, });
      return;
    }

    const FormData = createFormData(this.adminForm.value);
    if (this.selectedAdminId) {
      this.updateAdmin(FormData);
    } else {
      this.createAdmin(FormData);
    }
  }

  createAdmin(FormData: any) {
    this.loaderService.show();
    const url = 'admin';
    this.apiService.post(url, FormData).subscribe({
      next: (res: any) => {
        this.adminDialog = false;
        this.fetchAdmins();
        this.messageService.add({
          severity: 'success',
          summary: 'Successful',
          detail: 'Admin Added',
          life: 3000,
        });
        this.adminForm.reset();
      },
      error: (err: any) => {
        this.loaderService.hide();
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: err.error.message,
          life: 3000,
        });
      },
      complete: () => {
        this.loaderService.hide();
      },
    });
  }

  updateAdmin(FormData: any) {
    this.loaderService.show();
    const url = `admin/${this.selectedAdminId}`;
    this.apiService.put(url, FormData).subscribe({
      next: (res: any) => {
        this.adminDialog = false;
        this.fetchAdmins();
        this.messageService.add({
          severity: 'success',
          summary: 'Successful',
          detail: 'Admin Updated',
          life: 3000,
        });
        this.adminForm.reset();
        this.selectedAdminId = null;
      },
      error: (err: any) => {
        this.loaderService.hide();
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: err.error.message,
          life: 3000,
        });
      },
      complete: () => {
        this.loaderService.hide();
      },
    });
  }

  onFileSelected(event: Event) {
    const file = (event.target as HTMLInputElement).files?.[0];
    if (file) {
      this.adminForm.controls['image'].setValue(file);
      const reader = new FileReader();
      reader.onload = () => {
        this.adminForm.value.image_url = reader.result as string; // preview image
      };
      reader.readAsDataURL(file);

      // TODO: call API to upload
    }
  }
}
