import { Component, OnInit, inject } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ApiService } from '../../../../core/services/api.service';
import { MessageService } from 'primeng/api';
import { DialogService, DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { PrimengModules } from '../../../../core/utils/primeng';
import { LoaderService } from '../../../../core/services/loader.service';
import { ServerApiService } from '../../../../core/services/server-api.service';

@Component({
    selector: 'app-add-address-modal',
    imports: [PrimengModules],
    template: `
                <div class="w-full">
                    <form [formGroup]="addAddressForm">
                        <div class="grid grid-cols-1 md:grid-cols-1 gap-4">
                            <!-- currency -->
                             <div>
                                <label for="currency" class="required-label block font-bold mb-3">Currency</label>
                                <p-select [options]="currencies" optionLabel="name" optionValue="id" [showClear]="true"
                                    formControlName="currency" placeholder="Select a currency" [appendTo]="'body'" required
                                    fluid />
                                <app-form-error [control]="addAddressForm.controls['currency']" [controlName]="'Currency'"
                                    [apiErrorType]="'currency'" />
                            </div>
                        </div>
                    </form>
                </div>
                <div class="flex justify-end gap-2 mt-6 border-t border-gray-200 pt-2">
                    <p-button label="Cancel" icon="pi pi-times" text (click)="close()" />
                    <p-button label="Save" icon="pi pi-check" (click)="saveAddAddress()" />
                </div>
            `,
    providers: [DialogService],
})
export class AddAddressModal implements OnInit {
    private fb = inject(FormBuilder);
    private messageService = inject(MessageService);
    private ref = inject(DynamicDialogRef);
    private config = inject(DynamicDialogConfig);
    private loaderService = inject(LoaderService);
    private serverApiService = inject(ServerApiService);

    addAddressForm!: FormGroup;
    currencies = [{ id: "usd", name: "USD" }, { id: "gbp", name: "GBP" }, { id: "eur", name: "EUR" }];
    connectAccounts_id: any = null;
    financialAccount_id: any = null;

    ngOnInit() {
        this.connectAccounts_id = this.config.data.connectAccounts_id;
        this.financialAccount_id = this.config.data.financialAccount_id;
        this.createAddAddressForm();
    }

    createAddAddressForm() {
        this.addAddressForm = this.fb.group({
            currency: ['gbp', Validators.required],
        });
    }

    saveAddAddress() {
        if (!this.addAddressForm.valid) {
            this.addAddressForm.markAllAsTouched();
            this.addAddressForm.updateValueAndValidity();
            return;
        }
        this.loaderService.show();
        this.serverApiService.post(`connect/connect-accounts/${this.connectAccounts_id}/financial-accounts/${this.financialAccount_id}/addresses`, this.addAddressForm.value).subscribe({
            next: (res: any) => { this.ref.close(res); },
            error: (err: any) => {
                this.loaderService.hide();
                this.messageService.add({ severity: 'error', summary: 'Error', detail: err.error.message, life: 3000, });
            },
            complete: () => { this.loaderService.hide(); },
        });
    }

    close() {
        this.ref.close();
    }
}
