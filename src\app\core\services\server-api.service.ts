import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';

@Injectable({
    providedIn: 'root',
})
export class ServerApiService {
    public baseUrl = environment.serverBaseUrl;

    constructor(private http: HttpClient) { }

    request<T>(
        method: string,
        url: string,
        body?: any,
        params?: HttpParams,
        headers?: HttpHeaders,
    ): Observable<T> {
        const options = {
            params: params,
            headers: headers,
        };

        url = `${this.baseUrl}/${url}`;

        switch (method.toUpperCase()) {
            case 'GET':
                return this.http.get<T>(url, options);
            case 'POST':
                return this.http.post<T>(url, body, options);
            case 'PUT':
                return this.http.put<T>(url, body, options);
            case 'DELETE':
                return this.http.delete<T>(url, options);
            default:
                throw new Error(`Unsupported HTTP method: ${method}`);
        }
    }

    get<T>(url: string, params?: HttpParams, headers?: HttpHeaders): Observable<T> {
        return this.request<T>('GET', url, undefined, params, headers);
    }

    post<T>(url: string, body: any, params?: HttpParams, headers?: HttpHeaders): Observable<T> {
        return this.request<T>('POST', url, body, params, headers);
    }

    put<T>(url: string, body: any, params?: HttpParams, headers?: HttpHeaders): Observable<T> {
        return this.request<T>('PUT', url, body, params, headers);
    }

    delete<T>(url: string, params?: HttpParams, headers?: HttpHeaders): Observable<T> {
        return this.request<T>('DELETE', url, undefined, params, headers);
    }
}