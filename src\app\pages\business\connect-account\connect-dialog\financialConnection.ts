import { Component, OnInit, inject } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ApiService } from '../../../../core/services/api.service';
import { MessageService } from 'primeng/api';
import { DialogService, DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { PrimengModules } from '../../../../core/utils/primeng';
import { LoaderService } from '../../../../core/services/loader.service';
import { ServerApiService } from '../../../../core/services/server-api.service';
import { createFormData } from '../../../../core/utils/utils';

@Component({
    selector: 'app-financial-connection-modal',
    imports: [PrimengModules],
    template: `
                <div class="w-full">
                    <form [formGroup]="financialConnectionForm">
                        <div class="grid grid-cols-1 md:grid-cols-1 gap-4">
                            <div>
                                <label for="nickname" class="required-label block font-bold mb-3">Nickname</label>
                                <input type="text" aria-label="Nickname" pInputText id="nickname" formControlName="nickname"
                                    placeholder="Nickname" required autofocus fluid />
                                <app-form-error [control]="financialConnectionForm.controls['nickname']" [controlName]="'Nickname'"
                                    [apiErrorType]="'nickname'" />
                            </div>
                            <div>
                                <label for="currency" class="required-label block font-bold mb-3">Currency</label>
                                <p-select [options]="currencies" optionLabel="name" optionValue="id" [showClear]="true"
                                    formControlName="currency" placeholder="Select a currency" [appendTo]="'body'" required fluid />
                                <app-form-error [control]="financialConnectionForm.controls['currency']" [controlName]="'Currency'"
                                    [apiErrorType]="'currency'" />
                            </div>
                        </div>
                    </form>
                </div>
                <div class="flex justify-end gap-2 mt-6 border-t border-gray-200 pt-2">
                    <p-button label="Cancel" icon="pi pi-times" text (click)="close()" />
                    <p-button label="Save" icon="pi pi-check" (click)="saveFinancialConnection()" />
                </div>
            `,
    providers: [DialogService],
})
export class FinancialConnectionModal implements OnInit {
    private fb = inject(FormBuilder);
    private messageService = inject(MessageService);
    private ref = inject(DynamicDialogRef);
    private config = inject(DynamicDialogConfig);
    private loaderService = inject(LoaderService);
    private serverApiService = inject(ServerApiService);

    connectAccounts_id: any = null;
    financialConnectionForm!: FormGroup;
    currencies = [{ id: "usd", name: "USD" }, { id: "gbp", name: "GBP" }, { id: "eur", name: "EUR" }];

    ngOnInit() {
        this.connectAccounts_id = this.config.data.connectAccounts_id;
        this.createFinancialConnectionForm();
    }

    createFinancialConnectionForm() {
        this.financialConnectionForm = this.fb.group({
            nickname: ['', Validators.required],
            currency: ['gbp', Validators.required],
        });
    }

    saveFinancialConnection() {
        if (!this.financialConnectionForm.valid) {
            this.financialConnectionForm.markAllAsTouched();
            this.financialConnectionForm.updateValueAndValidity();
            this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Please fill all the required fields', life: 3000, });
            return;
        }

        let payload = this.financialConnectionForm.value;
        const FormData = createFormData(payload);
        this.loaderService.show();
        this.serverApiService.post(`connect/connect-accounts/${this.connectAccounts_id}/financial-accounts`, FormData).subscribe({
            next: (res: any) => { this.ref.close(res); },
            error: (err: any) => {
                this.loaderService.hide();
                this.messageService.add({ severity: 'error', summary: 'Error', detail: err.error.message, life: 3000, });
            },
            complete: () => { this.loaderService.hide(); },
        });
    }

    close() {
        this.ref.close();
    }
}
