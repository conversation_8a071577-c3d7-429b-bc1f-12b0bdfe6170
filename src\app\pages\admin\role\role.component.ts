import { Component, inject, ViewChild } from '@angular/core';
import { Table } from 'primeng/table';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { ConfirmationService, MessageService, TreeNode } from 'primeng/api';
import { debounceTime } from 'rxjs';
import { HttpParams } from '@angular/common/http';
import { PrimengModules } from '../../../core/utils/primeng';
import { ApiService } from '../../../core/services/api.service';
import { LoaderService } from '../../../core/services/loader.service';
import { createFormData, filterParams } from '../../../core/utils/utils';
import { PermissionsComponent } from '../permissions/permissions.component';
import { AuthService } from '../../../core/services/authentication.service';

@Component({
  selector: 'app-role',
  imports: [PrimengModules, PermissionsComponent],
  standalone: true,
  templateUrl: './role.component.html',
  styleUrl: './role.component.scss',
  providers: [ConfirmationService],
})
export class RoleComponent {
  private apiService = inject(ApiService);
  private loaderService = inject(LoaderService);
  private fb = inject(FormBuilder);
  private messageService = inject(MessageService);
  private confirmationService = inject(ConfirmationService);
  public authService = inject(AuthService);

  @ViewChild('dt') dt!: Table;

  filterForm!: FormGroup;
  roleForm!: FormGroup;
  search = new FormControl('');

  roles: any[] = [];
  totalRecords: number = 0;

  roleDialog = false;
  selectedRoleId: any = null;

  constructor() {
    this.createRoleForm();
    this.filterForm = this.fb.group({
      page: [],
      limit: [],
    });
  }
  ngOnInit() {
    this.search.valueChanges.pipe(debounceTime(400)).subscribe((val) => {
      this.fetchRoles();
    });
  }

  createRoleForm() {
    this.roleForm = this.fb.group({
      name: ['', Validators.required],
      description: ['', Validators.required],
      permissions: [''],
    });
  }

  fetchRoles() {
    const httpParams: HttpParams = filterParams({ ...this.filterForm.value, search: this.search.value, });
    this.loaderService.show();
    this.apiService.get('role', httpParams).subscribe({
      next: (res: any) => { this.roles = res.data.roles; this.totalRecords = res.data.totalItems; },
      error: (err: any) => { this.loaderService.hide(); },
      complete: () => { this.loaderService.hide(); },
    });
  }

  openDialogCreateRole() { this.roleDialog = true; this.createRoleForm(); }

  openDialogEditRole(role: any) {
    this.roleDialog = true;
    this.roleForm.reset();
    role.permissions = JSON.parse(role.permissions);
    this.roleForm.patchValue(role);
    this.selectedRoleId = role.id;
  }

  deleteRole(role: any) {
    this.confirmationService.confirm({
      message: 'Are you sure you want to delete ' + role.title + '?',
      header: 'Confirm',
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        this.loaderService.show();
        this.apiService.delete(`role/${role.id}`).subscribe({
          next: (res: any) => {
            this.messageService.add({ severity: 'success', summary: 'Successful', detail: `${role.title} deleted`, life: 3000 });
            this.fetchRoles();
          },
          error: (err: any) => { this.loaderService.hide(); },
          complete: () => { this.loaderService.hide(); },
        });
      },
    });
  }

  toggleStatus(event: any, role: any) {
    role.status = event.checked ? 'active' : 'inactive';
    const FormData = createFormData(role);
    this.loaderService.show();
    this.apiService.post('role', FormData).subscribe({
      next: (res: any) => {
        this.messageService.add({ severity: 'success', summary: 'Successful', detail: `${role.name} ${event.checked ? 'activated' : 'deactivated'}`, life: 3000, });
      },
      error: (err: any) => {
        this.loaderService.hide();
      },
      complete: () => {
        this.loaderService.hide();
      },
    });
  }

  onLazyLoad(event: any) {
    if (
      event.first >= 0 &&
      event.rows &&
      (this.filterForm.value.page !== event.first / event.rows + 1 ||
        this.filterForm.value.limit !== event.rows)
    ) {
      this.filterForm.controls['page'].setValue(event.first / event.rows + 1, {
        emitEvent: false,
      });
      this.filterForm.controls['limit'].setValue(event.rows, {
        emitEvent: false,
      });
      this.fetchRoles();
    }
    if (event.sortField && event.sortOrder) {
      this.roles.sort((a, b) => {
        if (a[event.sortField] < b[event.sortField]) {
          return event.sortOrder === 1 ? -1 : 1;
        }
        if (a[event.sortField] > b[event.sortField]) {
          return event.sortOrder === 1 ? 1 : -1;
        }
        return 0;
      });
    }
  }

  closeDialog() { this.roleDialog = false; this.roleForm.reset(); }

  saveRole() {
    if (!this.roleForm.valid) {
      this.roleForm.markAllAsTouched();
      this.roleForm.updateValueAndValidity();
      this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Please fill all the required fields', life: 3000, });
      return;
    }
    const FormData = createFormData(this.roleForm.value);
    if (this.selectedRoleId) {
      this.updateRole(FormData);
    } else {
      this.createRole(FormData);
    }
  }

  createRole(FormData: any) {
    this.loaderService.show();
    const url = 'role';
    this.apiService.post(url, FormData).subscribe({
      next: (res: any) => {
        this.roleDialog = false;
        this.fetchRoles();
        this.messageService.add({ severity: 'success', summary: 'Successful', detail: 'Added Role', life: 3000, });
        this.roleForm.reset();
      },
      error: (err: any) => {
        this.loaderService.hide();
        this.messageService.add({ severity: 'error', summary: 'Error', detail: err.error.message, life: 3000, });
      },
      complete: () => { this.loaderService.hide(); },
    });
  }

  updateRole(FormData: any) {
    this.loaderService.show();
    const url = `role/${this.selectedRoleId}`;
    this.apiService.put(url, FormData).subscribe({
      next: (res: any) => {
        this.roleDialog = false;
        this.fetchRoles();
        this.messageService.add({ severity: 'success', summary: 'Successful', detail: 'Updated Role', life: 3000, });
        this.roleForm.reset();
        this.selectedRoleId = null;
      },
      error: (err: any) => {
        this.loaderService.hide();
        this.messageService.add({ severity: 'error', summary: 'Error', detail: err.error.message, life: 3000, });
      },
      complete: () => { this.loaderService.hide(); },
    });
  }
}
