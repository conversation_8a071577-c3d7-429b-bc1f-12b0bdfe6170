<p-table #dt [value]="businesses" [rows]="10" [paginator]="true" [resetPageOnSort]="false"
    [globalFilterFields]="['name', 'trading_name', 'email']" [lazy]="true" [tableStyle]="{ 'min-width': '70rem' }"
    [scrollable]="true" scrollHeight="80vh" (onLazyLoad)="onLazyLoad($event)" [rowHover]="true" dataKey="id"
    [totalRecords]="totalRecords" currentPageReportTemplate="Showing {first} to {last} of {totalRecords} businesses"
    [showCurrentPageReport]="true" [rowsPerPageOptions]="[10, 20, 30]" >
    <ng-template #caption>
        <p-toolbar [style]="{'border': 'none','padding': '0'}">
            <ng-template #start>
                <p class="font-medium font-bold text-2xl">Businesses</p>
            </ng-template>

            <ng-template #end>
                <p-iconfield class="mr-2">
                    <p-inputicon styleClass="pi pi-search" />
                    <input pInputText type="text" [formControl]="search" placeholder="Search..." />
                </p-iconfield>
                @if(authService._superman || authService._permissions?.businesses?.actions?.add) {
                <p-button label="New" icon="pi pi-plus" severity="secondary" class="mr-2"
                    (click)="openDialogCreateBusiness()" />
                }
                <!-- <p-button label="Export" icon="pi pi-download" severity="secondary" class="mr-2"  /> -->
            </ng-template>
        </p-toolbar>

    </ng-template>
    <ng-template #header>
        <tr>
            <th pSortableColumn="name">
                Name
                <p-sortIcon field="name" />
            </th>
            <th pSortableColumn="trading_name">
                Trading Name
                <p-sortIcon field="trading_name" />
            </th>
            <th pSortableColumn="email">
                Email
                <p-sortIcon field="email" />
            </th>
            <th>
                Disabled
            </th>
            <th>Actions</th>
        </tr>
    </ng-template>
    <ng-template #body let-business>
        <tr>
            <td>{{ business?.name }}</td>
            <td>{{ business?.trading_name }}</td>
            <td>{{ business?.email }}</td>
            <td>
                <p-toggleswitch [ngModel]="business?.disabled ? true : false"
                    (onChange)="toggleStatus($event, business)" />
            </td>
            <td>
                <p-button icon="pi pi-eye" class="mr-2" [rounded]="true" [outlined]="true"
                    (click)="viewBusiness(business)" />
                @if(authService._superman || authService._permissions?.businesses?.actions?.edit) {
                <p-button icon="pi pi-pencil" class="mr-2" [rounded]="true" [outlined]="true"
                    (click)="openDialogEditBusiness(business)" />
                }
                @if(authService._superman || authService._permissions?.businesses?.actions?.delete) {
                <p-button icon="pi pi-trash" severity="danger" [rounded]="true" [outlined]="true"
                    (click)="deleteBusiness(business)" />
                }
            </td>
        </tr>

    </ng-template>
    <ng-template #emptymessage>
        <tr>
            <td colspan="5" style="text-align: center !important;">No records found</td>
        </tr>
    </ng-template>
</p-table>

@if(businessDialog){
<app-add-business [(businessDialog)]="businessDialog" (save)="handleSave($event)" [businessData]="selectedBusiness"
    [isEdit]="isEdit"></app-add-business>
}

<p-confirmdialog [style]="{ width: '450px' }" />