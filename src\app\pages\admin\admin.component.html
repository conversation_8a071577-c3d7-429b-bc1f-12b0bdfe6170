<p-toolbar styleClass="mb-6" [style]="{'border': 'none','padding': '0','background': 'none'}">
  <ng-template #start>
    <div class="flex flex-col">
      <p class="font-bold text-2xl !m-0">Admins</p>
      <p class="text-md text-gray-600">Manage your admins</p>
    </div>
  </ng-template>

  <ng-template #end>
    <p-button class="mr-2" outlined pTooltip="Fund Transfer" tooltipPosition="top" size="large">
      <i class="fa-solid fa-file-pdf" style="color: #ff0000"></i>
    </p-button>
    <p-button class="mr-2" outlined pTooltip="Fund Transfer" tooltipPosition="top" size="large">
      <i class="fa-solid fa-file-excel" style="color: #008000"></i>
    </p-button>
    <!-- print -->
    <p-button class="mr-2" outlined pTooltip="Fund Transfer" tooltipPosition="top" size="large">
      <i class="fa-solid fa-print" style="color: #000000"></i>
    </p-button>

    @if(authService._superman || authService._permissions?.admins?.actions?.add) {
    <p-button label="New" icon="pi pi-plus" class="mr-2" (click)="openDialogCreateAdmin()" />
    }
  </ng-template>
</p-toolbar>

<div class="bg-white p-2 border border-gray-200 rounded-md">
  <p-table #dt [value]="admins" [rows]="10" [paginator]="true" [resetPageOnSort]="false"
    [globalFilterFields]="['name', 'email', 'mobile_number']" [lazy]="true" (onLazyLoad)="onLazyLoad($event)"
    [rowHover]="true" dataKey="id" [totalRecords]="totalRecords" [tableStyle]="{ 'min-width': '80rem' }"
    [scrollable]="true" scrollHeight="80vh"
    currentPageReportTemplate="Showing {first} to {last} of {totalRecords} admins" [showCurrentPageReport]="true"
    [rowsPerPageOptions]="[10, 20, 30]">
    <ng-template #caption>
      <p-toolbar [style]="{'border': 'none','padding': '0'}">
        <ng-template #start>
          <p-iconfield class="mr-2">
            <p-inputicon styleClass="pi pi-search" />
            <input pInputText type="text" [formControl]="search" placeholder="Search..." />
          </p-iconfield>
        </ng-template>
        <ng-template #end>
          <p-button icon="pi pi-filter" class="mr-2" />
        </ng-template>
      </p-toolbar>
    </ng-template>
    <ng-template #header>
      <tr>
        <th pSortableColumn="name" pFrozenColumn>
          Name
          <p-sortIcon field="name" />
        </th>
        <th pSortableColumn="email">
          Email
          <p-sortIcon field="email" />
        </th>
        <th pSortableColumn="mobile_number">
          Mobile Number
          <p-sortIcon field="mobile_number" />
        </th>
        <th pSortableColumn="username">
          User Name
          <p-sortIcon field="username" />
        </th>
        <th pSortableColumn="role">
          Role
          <p-sortIcon field="role" />
        </th>
        <th>
          Status
        </th>
        <th>Actions</th>
      </tr>
    </ng-template>
    <ng-template #body let-admin>
      <tr>
        <td pFrozenColumn>{{ admin.name }}</td>
        <td>{{ admin.email }}</td>
        <td>{{ admin.mobile_number }}</td>
        <td>{{ admin.username }}</td>
        <td>{{ admin.role }}</td>
        <td>
          <p-toggleswitch [ngModel]="admin.disabled ? true : false" (onChange)="toggleStatus($event, admin)" />
        </td>
        <td>
          @if (!admin.superman) {
          @if(authService._superman || authService._permissions?.admins?.actions?.edit) {
          <p-button icon="pi pi-pen-to-square" class="mr-2" [outlined]="true" severity="info"
            (click)="openDialogEditAdmin(admin)" />
          }
          @if(authService._superman || authService._permissions?.admins?.actions?.delete) {
          <p-button icon="pi pi-trash" severity="danger" [outlined]="true" (click)="deleteAdmin(admin)" />
          }
          }
        </td>
      </tr>

    </ng-template>
    <ng-template #emptymessage>
      <tr>
        <td colspan="7" style="text-align: center !important;">No records found</td>
      </tr>
    </ng-template>
  </p-table>
</div>

<p-drawer header="{{ adminForm.get('id')?.value ? 'Edit' : 'Add' }} Admin" [(visible)]="adminDialog" [autoZIndex]="true"
  position="right" (onHide)="closeDialog()" styleClass="!w-full md:!w-[50rem] lg:!w-[60rem]" [modal]="true"
  [dismissible]="false">
  <ng-template #content>
    <form [formGroup]="adminForm" (ngSubmit)="saveAdmin()">
      <!-- image -->
      <div class="flex justify-center mb-4">
        <div class="relative w-36 h-36 rounded-full overflow-hidden cursor-pointer group">
          <img [src]="adminForm.value?.image_url" alt="Profile" class="w-full h-full object-cover rounded-full"
            onerror="this.src='images/no-user.png'" />
          <div
            class="absolute inset-0 bg-black/50 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300"
            (click)="fileInput.click()">
            <i class="pi pi-camera text-white text-3xl"></i>
          </div>
          <input type="file" #fileInput accept="image/*" (change)="onFileSelected($event)" class="hidden" />
        </div>
      </div>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <div>
          <label for="name" class="required-label block font-bold mb-3">Name</label>
          <input type="text" aria-label="Name" pInputText id="name" formControlName="name" placeholder="Name" autofocus
            fluid />
          <app-form-error [control]="adminForm.controls['name']" [controlName]="'Name'" [apiErrorType]="'name'" />
        </div>
        <div>
          <label for="email" class="required-label block font-bold mb-3">Email</label>
          <input type="text" aria-label="Email" pInputText id="email" formControlName="email" placeholder="Email"
            autofocus fluid />
          <app-form-error [control]="adminForm.controls['email']" [controlName]="'Email'" [apiErrorType]="'email'" />
        </div>
        <div>
          <label for="mobile_number" class="block font-bold mb-3">Mobile Number</label>
          <input type="text" aria-label="Mobile Number" pInputText id="mobile_number" formControlName="mobile_number"
            placeholder="Mobile Number" autofocus fluid />
          <app-form-error [control]="adminForm.controls['mobile_number']" [controlName]="'Mobile Number'"
            [apiErrorType]="'mobile_number'" />
        </div>
        <div>
          <label for="role" class="required-label block font-bold mb-3">Role</label>
          <p-select [options]="roles" optionLabel="title" optionValue="title" [showClear]="true" formControlName="role"
            placeholder="Select a role" [appendTo]="'body'" class="w-full" />
          <app-form-error [control]="adminForm.controls['role']" [controlName]="'Role'" [apiErrorType]="'role'" />
        </div>
        <div>
          <label for="username" class="required-label block font-bold mb-3">Username</label>
          <input type="text" aria-label="Username" pInputText id="username" formControlName="username"
            placeholder="Username" autofocus fluid />
          <app-form-error [control]="adminForm.controls['username']" [controlName]="'Username'"
            [apiErrorType]="'username'" />
        </div>
        <div>
          <label for="password" class="required-label block font-bold mb-3">Password</label>
          <input type="password" aria-label="Password" pInputText id="password" formControlName="password"
            placeholder="Password" autofocus fluid />
          <app-form-error [control]="adminForm.controls['password']" [controlName]="'Password'"
            [apiErrorType]="'password'" />
        </div>
        <!-- <div>
                    <label for="superman" class="block font-bold mb-3">Superman</label>
                    <p-checkbox formControlName="superman" id="superman" binary class="mr-2"></p-checkbox>
                </div> -->

      </div>
      <!-- Permissions Section -->
      <div class="mt-6">
        <h3 class="font-bold text-lg text-gray-800">Permissions</h3>
        <p class="text-sm text-gray-600">Select the permissions for this admin</p>
      </div>
      <app-permissions [(permissions)]="adminForm.value.permissions"></app-permissions>
      <button type="submit" style="display: none"></button>
    </form>
  </ng-template>
  <ng-template #footer>
    <div class="flex items-center gap-2">
      <button pButton label="Cancel" icon="pi pi-times" class="w-full" outlined (click)="closeDialog()"></button>
      <button pButton label="Save" icon="pi pi-check" class="w-full" (click)="saveAdmin()"></button>
    </div>
  </ng-template>
</p-drawer>

<p-confirmdialog [style]="{ width: '450px' }" />