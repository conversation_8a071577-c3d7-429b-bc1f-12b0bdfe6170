import {
  Component,
  EventEmitter,
  inject,
  Input,
  OnChanges,
  OnInit,
  Output,
} from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ApiService } from '../../../core/services/api.service';
import { LoaderService } from '../../../core/services/loader.service';
import { createFormData } from '../../../core/utils/utils';
import { MessageService } from 'primeng/api';
import { PrimengModules } from '../../../core/utils/primeng';
import { HttpParams } from '@angular/common/http';

@Component({
  selector: 'app-add-business',
  imports: [PrimengModules],
  templateUrl: './add-business.component.html',
  styleUrl: './add-business.component.scss',
  standalone: true,
  providers: [],
})
export class AddBusinessComponent implements OnInit, OnChanges {
  private apiService = inject(ApiService);
  private loaderService = inject(LoaderService);
  private fb = inject(FormBuilder);
  private messageService = inject(MessageService);

  @Input() businessDialog: boolean = false;
  @Output() businessDialogChange = new EventEmitter<boolean>();
  @Output() save = new EventEmitter<any>();
  @Input() businessData: any = null;
  @Input() isEdit: boolean = false;

  businessForm!: FormGroup;
  countries: any[] = [];
  business_types = [
    { id: 'Sole trader', name: 'Sole trader' },
    { id: 'Partnership', name: 'Partnership' },
    { id: 'Limited company', name: 'Limited company' },
    { id: 'Not registered comapny', name: 'Not registered comapny' },
    { id: 'Other', name: 'Other' },
  ];

  fee_types: any[] = [{ label: 'Percentage', value: 'percentage' }, { label: 'Flat', value: 'flat' }];
  is_countries_loaded = false;

  constructor() {
    this.createBusinessForm();
    this.fetchCountries();
  }

  ngOnInit() { }

  ngOnChanges() {
    if (this.isEdit && this.businessData.id) {
      this.fetchBusines();
    } else {
      this.createBusinessForm();
    }
  }

  fetchBusines() {
    this.loaderService.show();
    this.apiService.get(`businesses/${this.businessData.id}`).subscribe({
      next: (res: any) => {
        this.businessForm.patchValue(res);
        this.businessForm.controls['joining_date'].setValue(new Date(res.joining_date));
        this.businessForm.controls['domain_valid_till'].setValue(new Date(res.domain_valid_till));
        this.businessForm.controls['company_date_of_creation'].setValue(new Date(res.company_date_of_creation));
      },
      error: (err: any) => { this.loaderService.hide(); },
      complete: () => { this.loaderService.hide(); },
    });
  }

  fetchCountries() {
    this.is_countries_loaded = true;
    this.apiService.get('countries/mini').subscribe({
      next: (res: any) => { this.countries = res; },
      error: (err: any) => { this.is_countries_loaded = false; },
      complete: () => { this.is_countries_loaded = false; },
    });
  }

  createBusinessForm() {
    this.businessForm = this.fb.group({
      id: [''],
      name: ['', Validators.required],
      trading_name: ['', Validators.required],
      type: ['', Validators.required],
      postcode: ['', Validators.required],
      door_no: [''],
      address: ['', Validators.required],
      city: ['', Validators.required],
      state: ['', Validators.required],
      country_id: [''],
      email: ['', [Validators.email, Validators.required]],
      contact_numbers: [''],
      joining_date: ['', Validators.required],
      domain: [''],
      domain_valid_till: [''],
      statement_description: ['', Validators.minLength(5)],
      statement_descriptor: ['', Validators.minLength(5)],
      company_number: [''],
      company_name: [''],
      company_type: [''],
      company_address_line_1: [''],
      company_address_line_2: [''],
      company_locality: [''],
      company_postal_code: [''],
      company_country: [''],
      company_region: [''],
      company_date_of_creation: [''],
      company_status: ['active'],
      business_commissions: this.fb.group({
        baas_in_fee_type: ['percentage'],
        baas_in_fee: [0],
        baas_out_fee_type: ['percentage'],
        baas_out_fee: [0],
      }),
    });
  }

  saveBusiness() {
    if (!this.businessForm.valid) {
      this.businessForm.markAllAsTouched();
      this.businessForm.updateValueAndValidity();
      this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Please fill all the required fields', life: 3000, });
      return;
    }
    const FormData = createFormData(this.businessForm.value);
    const url = this.businessForm.value.id ? `businesses/${this.businessForm.value.id}` : 'businesses';
    this.loaderService.show();
    this.apiService.post(url, FormData).subscribe({
      next: (res: any) => {
        this.businessDialog = false;
        this.businessDialogChange.emit(false);
        this.save.emit(res.data);
        this.messageService.add({ severity: 'success', summary: 'Successful', detail: `${this.isEdit ? 'Updated' : 'Added'} Business`, life: 3000, });
      },
      error: (err: any) => {
        this.loaderService.hide();
        this.messageService.add({ severity: 'error', summary: 'Error', detail: err.error.message, life: 3000, });
      },
      complete: () => { this.loaderService.hide(); },
    });
  }

  lookupPostcode() {
    if (!this.businessForm.controls['postcode'].value) {
      this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Please enter postcode', life: 3000, });
      return;
    }
    this.loaderService.show();
    this.apiService.get(`postcodes/find/${this.businessForm.controls['postcode'].value}`).subscribe({
      next: (res: any) => {
        this.businessForm.controls['postcode'].setValue(res.post_code);
        this.businessForm.controls['door_no'].setValue(res.thorough_fare);
        this.businessForm.controls['address'].setValue(res.street);
        this.businessForm.controls['city'].setValue(res.post_town);
      },
      error: (err: any) => {
        this.loaderService.hide();
        this.messageService.add({ severity: 'error', summary: 'Error', detail: err.error.message, life: 3000, });
      },
      complete: () => { this.loaderService.hide(); },
    });
  }

  lookupCompany() {
    if (!this.businessForm.controls['company_number'].value) {
      this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Please enter company number', life: 3000, });
      return;
    }
    this.loaderService.show();
    this.apiService.get(`businesses/company-number/${this.businessForm.controls['company_number'].value}`).subscribe({
      next: (res: any) => {
        this.businessForm.patchValue({
          company_name: res.company_name,
          company_type: res.type,
          company_address_line_1: res.registered_office_address.address_line_1,
          company_address_line_2: res.registered_office_address.address_line_2,
          company_locality: res.registered_office_address.locality,
          company_postal_code: res.registered_office_address.postal_code,
          company_country: res.registered_office_address.country,
          company_region: res.registered_office_address.region,
          company_date_of_creation: new Date(res.date_of_creation),
          company_status: res.company_status
        });
      },
      error: (err: any) => {
        this.loaderService.hide();
        this.messageService.add({ severity: 'error', summary: 'Error', detail: err.error.message, life: 3000, });
      },
      complete: () => { this.loaderService.hide(); },
    });
  }

  closeDialog() { this.businessDialog = false; this.businessDialogChange.emit(false); }
}
