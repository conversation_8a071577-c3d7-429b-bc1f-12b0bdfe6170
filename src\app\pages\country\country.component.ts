import { Component, inject, OnInit, ViewChild } from '@angular/core';
import { Table, TableLazyLoadEvent, TablePageEvent } from 'primeng/table';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { ConfirmationService, MessageService } from 'primeng/api';
import { ActivatedRoute, Router } from '@angular/router';
import { debounceTime } from 'rxjs';
import { HttpParams } from '@angular/common/http';
import { createFormData, filterParams } from '../../core/utils/utils';
import { PrimengModules } from '../../core/utils/primeng';
import { ApiService } from '../../core/services/api.service';
import { LoaderService } from '../../core/services/loader.service';
import { AuthService } from '../../core/services/authentication.service';

@Component({
  selector: 'app-country',
  standalone: true,
  imports: [PrimengModules],
  templateUrl: './country.component.html',
  styleUrl: './country.component.scss',
  providers: [ConfirmationService],
})
export class CountryComponent {
  private apiService = inject(ApiService);
  private loaderService = inject(LoaderService);
  private fb = inject(FormBuilder);
  private messageService = inject(MessageService);
  private confirmationService = inject(ConfirmationService);
  private router = inject(Router);
  public authService = inject(AuthService);

  @ViewChild('dt') dt!: Table;

  filterForm!: FormGroup;
  countryForm!: FormGroup;
  search = new FormControl('');
  countries: any[] = [];
  totalRecords: number = 0;
  countryDialog = false;
  selectedCountryId: any = null;

  constructor() {
    this.createCountryForm();
    this.filterForm = this.fb.group({
      page: [],
      limit: [],
    });
  }

  ngOnInit() {
    this.search.valueChanges.pipe(debounceTime(400)).subscribe((val) => {
      this.fetchCountries();
    });
  }

  fetchCountries() {
    const httpParams: HttpParams = filterParams({
      ...this.filterForm.value,
      query: this.search.value,
    });
    this.loaderService.show();
    this.apiService.get('country', httpParams).subscribe({
      next: (res: any) => {
        this.countries = res.data.countries;
        this.totalRecords = res.totalItems;
      },
      error: (err: any) => {
        this.loaderService.hide();
      },
      complete: () => {
        this.loaderService.hide();
      },
    });
  }

  openDialogCreateCountry() {
    this.countryDialog = true;
    this.countryForm.reset();
  }

  openDialogEditCountry(country: any) {
    this.countryDialog = true;
    this.countryForm.reset();
    this.countryForm.patchValue(country);
    this.selectedCountryId = country.id;
    this.countryForm.controls['phone_code'].setValue(country.phone_code.replace('+', ''));
  }

  deleteCountry(country: any) {
    this.confirmationService.confirm({
      message: 'Are you sure you want to delete ' + country.name + '?',
      header: 'Confirm',
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        this.loaderService.show();
        this.apiService.delete(`country/${country.id}`).subscribe({
          next: (res: any) => {
            this.messageService.add({
              severity: 'success',
              summary: 'Successful',
              detail: `${country.name} deleted`,
              life: 3000,
            });
            this.fetchCountries();
          },
          error: (err: any) => {
            this.loaderService.hide();
          },
          complete: () => {
            this.loaderService.hide();
          },
        });
      },
    });
  }

  onLazyLoad(event: any) {
    if (
      event.first >= 0 &&
      event.rows &&
      (this.filterForm.value.page !== event.first / event.rows + 1 ||
        this.filterForm.value.limit !== event.rows)
    ) {
      this.filterForm.controls['page'].setValue(event.first / event.rows + 1, {
        emitEvent: false,
      });
      this.filterForm.controls['limit'].setValue(event.rows, {
        emitEvent: false,
      });
      this.fetchCountries();
    }
    if (event.sortField && event.sortOrder) {
      this.countries.sort((a, b) => {
        if (a[event.sortField] < b[event.sortField]) {
          return event.sortOrder === 1 ? -1 : 1;
        }
        if (a[event.sortField] > b[event.sortField]) {
          return event.sortOrder === 1 ? 1 : -1;
        }
        return 0;
      });
    }
  }

  closeDialog() {
    this.countryDialog = false;
    this.countryForm.reset();
  }

  createCountryForm() {
    this.countryForm = this.fb.group({
      name: ['', Validators.required],
      iso: ['', Validators.required],
      iso3: ['', Validators.required],
      phone_code: ['', Validators.required],
      currency_name: ['', Validators.required],
      currency_code: ['', Validators.required],
      currency_symbol: ['', Validators.required],
      enabled: [true, Validators.required],
    });
  }

  saveCountry() {
    if (!this.countryForm.valid) {
      Object.values(this.countryForm.controls).forEach((control) => {
        control.markAsTouched();
        control.updateValueAndValidity();
      });
      return;
    }
    this.countryForm.value.phone_code = '+' + this.countryForm.value.phone_code;
    const FormData = createFormData(this.countryForm.value);

    if (this.selectedCountryId) {
      this.updateCountry(FormData);
    } else {
      this.createCountry(FormData);
    }
  }

  createCountry(FormData: any) {
    this.loaderService.show();
    const url = 'country';
    this.apiService.post(url, FormData).subscribe({
      next: (res: any) => {
        this.countryDialog = false;
        this.fetchCountries();
        this.messageService.add({
          severity: 'success',
          summary: 'Successful',
          detail: 'Added Country',
          life: 3000,
        });
        this.countryForm.reset();
      },
      error: (err: any) => {
        this.loaderService.hide();
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: err.error.message,
          life: 3000,
        });
      },
      complete: () => {
        this.loaderService.hide();
      },
    });
  }

  updateCountry(FormData: any) {
    this.loaderService.show();
    const url = `country/${this.selectedCountryId}`;
    this.apiService.put(url, FormData).subscribe({
      next: (res: any) => {
        this.countryDialog = false;
        this.fetchCountries();
        this.messageService.add({
          severity: 'success',
          summary: 'Successful',
          detail: 'Updated Country',
          life: 3000,
        });
        this.countryForm.reset();
        this.selectedCountryId = null;
      },
      error: (err: any) => {
        this.loaderService.hide();
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: err.error.message,
          life: 3000,
        });
      },
      complete: () => {
        this.loaderService.hide();
      },
    });
  }

}
