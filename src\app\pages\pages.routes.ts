import { Routes } from '@angular/router';
import { DashboardComponent } from './dashboard/dashboard.component';
import { BusinessComponent } from './business/business.component';
import { ViewBusinessComponent } from './business/view-business/view-business.component';
import { VendorComponent } from './vendor/vendor.component';
import { CountryComponent } from './country/country.component';
import { AdminComponent } from './admin/admin.component';
import { RoleComponent } from './admin/role/role.component';
import { ViewVendorComponent } from './vendor/view-vendor/view-vendor.component';
import { TransactionComponent } from './transactions/transaction/transaction.component';
import { ReceiveCreditsComponent } from './transactions/receive-credits/receive-credits.component';
import { permissionGuard } from '../core/guards/permission.guard';
import { SiteSettingComponent } from './site-setting/site-setting.component';
import { ApiDocumentComponent } from './api-document/api-document.component';
import { AccountOwnerComponent } from './account/account-owner/account-owner.component';
import { FranchiseOwnerComponent } from './franchise/franchise-owner/franchise-owner.component';

export default [
  { path: 'home', component: DashboardComponent },
  { path: 'business', canActivate: [permissionGuard], data: { permission: 'businesses.actions.list' }, component: BusinessComponent },
  { path: 'business/:id', canActivate: [permissionGuard], data: { permission: 'businesses.actions.list' }, component: ViewBusinessComponent },
  { path: 'vendors', canActivate: [permissionGuard], data: { permission: 'vendors.actions.list' }, component: VendorComponent },
  { path: 'view-vendor/:id', canActivate: [permissionGuard], data: { permission: 'vendors.actions.list' }, component: ViewVendorComponent },
  { path: 'country', canActivate: [permissionGuard], data: { permission: 'country.actions.list' }, component: CountryComponent },
  { path: 'site-settings', canActivate: [permissionGuard], data: { permission: 'site_settings.actions.list' }, component: SiteSettingComponent },
  { path: 'admins', canActivate: [permissionGuard], data: { permission: 'admins.actions.list' }, component: AdminComponent },
  { path: 'admin/role', canActivate: [permissionGuard], data: { permission: 'roles.actions.list' }, component: RoleComponent },
  { path: 'transactions', canActivate: [permissionGuard], data: { permission: 'transactions.actions.list' }, component: TransactionComponent },
  { path: 'receive-credit', canActivate: [permissionGuard], data: { permission: 'receive_credit.actions.list' }, component: ReceiveCreditsComponent },
  { path: 'api-document', canActivate: [permissionGuard], data: { permission: 'receive_credit.actions.list' }, component: ApiDocumentComponent },
  { path: 'account-owner', canActivate: [permissionGuard], data: { permission: 'account_owner.actions.list' }, component: AccountOwnerComponent },
  { path: 'account-owner', canActivate: [permissionGuard], data: { permission: 'account_owner.actions.list' }, component: AccountOwnerComponent },
  { path: 'franchise/owner', canActivate: [permissionGuard], data: { permission: 'owners.actions.list' }, component: FranchiseOwnerComponent },
] as Routes;
