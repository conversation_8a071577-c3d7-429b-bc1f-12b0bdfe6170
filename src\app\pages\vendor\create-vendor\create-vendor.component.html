<p-drawer header="{{ isEdit ? 'Edit' : 'Add' }} Vendor" [(visible)]="vendorDialog"
    (visibleChange)="vendorDialogChange.emit($event)" [autoZIndex]="true" position="right"
    styleClass="!w-full md:!w-[50rem] lg:!w-[50rem]" [modal]="true" [dismissible]="false">
    <ng-template #content>
        <form [formGroup]="vendorForm">
            <div class="flex justify-center mb-4">
                <div class="relative w-36 h-36 rounded-full overflow-hidden cursor-pointer group">
                    <img [src]="vendorData?.image_url" alt="Profile" class="w-full h-full object-cover rounded-full"
                        onerror="this.src='images/no-user.png'" />
                    <div class="absolute inset-0 bg-black/50 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                        (click)="fileInput.click()">
                        <i class="pi pi-camera text-white text-3xl"></i>
                    </div>
                    <input type="file" #fileInput accept="image/*" (change)="onFileSelected($event)" class="hidden" />
                </div>
            </div>


            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                    <label for="first_name" class="required-label block font-bold mb-3">First Name</label>
                    <input type="text" aria-label="First Name" pInputText id="first_name" formControlName="first_name"
                        placeholder="First Name" autofocus fluid />
                    <app-form-error [control]="vendorForm.controls['first_name']" [controlName]="'First Name'"
                        [apiErrorType]="'first_name'" />
                </div>
                <div>
                    <label for="last_name" class="block font-bold mb-3">Last Name</label>
                    <input type="text" aria-label="Last Name" pInputText id="last_name" formControlName="last_name"
                        placeholder="Last Name" autofocus fluid />
                    <app-form-error [control]="vendorForm.controls['last_name']" [controlName]="'Last Name'"
                        [apiErrorType]="'last_name'" />
                </div>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                    <label for="contact_emails" class="block font-bold mb-3">Contact Emails</label>
                    <p-autocomplete formControlName="contact_emails" inputId="contact_emails" multiple fluid
                        [suggestions]="emails" (completeMethod)="searchEmail($event)" placeholder="Enter Email" />
                    <app-form-error [control]="vendorForm.controls['contact_emails']" [controlName]="'Contact Email'"
                        [apiErrorType]="'contact_emails'" />
                </div>
                <div>
                    <label for="contact_numbers" class="block font-bold mb-3">Contact Numbers</label>
                    <p-autocomplete formControlName="contact_numbers" inputId="contact_numbers" multiple fluid
                        [typeahead]="false" placeholder="Enter Number" [suggestions]="numbers"
                        (completeMethod)="searchNumber($event)" />
                    <app-form-error [control]="vendorForm.controls['contact_numbers']" [controlName]="'Contact Numbers'"
                        [apiErrorType]="'contact_numbers'" />
                </div>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div>
                    <label for="dob" class="required-label block font-bold mb-3">Date of Birth</label>
                    <p-datePicker formControlName="dob" [showIcon]="true" [appendTo]="'body'" styleClass="w-full"
                        dateFormat="dd-mm-yy" />
                    <app-form-error [control]="vendorForm.controls['dob']" [controlName]="'Date of Birth'"
                        [apiErrorType]="'dob'" />
                </div>
                <div>
                    <label for="login_email" class="required-label block font-bold mb-3">Login Email</label>
                    <input type="text" aria-label="Login Email" pInputText id="login_email"
                        formControlName="login_email" placeholder="Login Email" autofocus fluid />
                    <app-form-error [control]="vendorForm.controls['login_email']" [controlName]="'Login Email'"
                        [apiErrorType]="'login_email'" />
                </div>
                <div>
                    <label for="password" class="required-label block font-bold mb-3">Password</label>
                    <p-password id="password" formControlName="password" placeholder="Enter Password"
                        [toggleMask]="true" [fluid]="true"></p-password>
                    <app-form-error [control]="vendorForm.controls['password']" [controlName]="'Password'"
                        [apiErrorType]="'password'" />
                </div>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div>
                    <label for="postcode" class="block font-bold mb-3">Postcode</label>
                    <input type="text" aria-label="Postcode" pInputText id="postcode" formControlName="postcode"
                        placeholder="Postcode" autofocus fluid />
                    <app-form-error [control]="vendorForm.controls['postcode']" [controlName]="'Postcode'"
                        [apiErrorType]="'postcode'" />
                </div>
                <div>
                    <label for="door_no" class="block font-bold mb-3">Door No</label>
                    <input type="text" aria-label="Door No" pInputText id="door_no" formControlName="door_no"
                        placeholder="Door No" autofocus fluid />
                    <app-form-error [control]="vendorForm.controls['door_no']" [controlName]="'Door No'"
                        [apiErrorType]="'door_no'" />
                </div>
                <div>
                    <label for="residential_address" class="required-label block font-bold mb-3">Residential
                        Address</label>
                    <input type="text" aria-label="Residential Address" pInputText id="residential_address"
                        formControlName="residential_address" placeholder="Residential Address" autofocus fluid />
                    <app-form-error [control]="vendorForm.controls['residential_address']"
                        [controlName]="'Residential Address'" [apiErrorType]="'residential_address'" />
                </div>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div>
                    <label for="city" class="block font-bold mb-3">City</label>
                    <input type="text" aria-label="City" pInputText id="city" formControlName="city" placeholder="City"
                        autofocus fluid />
                    <app-form-error [control]="vendorForm.controls['city']" [controlName]="'City'"
                        [apiErrorType]="'city'" />
                </div>
                <div>
                    <label for="state" class="block font-bold mb-3">State</label>
                    <input type="text" aria-label="State" pInputText id="state" formControlName="state"
                        placeholder="State" autofocus fluid />
                    <app-form-error [control]="vendorForm.controls['state']" [controlName]="'State'"
                        [apiErrorType]="'state'" />
                </div>
                <div>
                    <label for="country_id" class="required-label block font-bold mb-3">Country</label>
                    <p-select [options]="countries" optionLabel="name" optionValue="id" [showClear]="true"
                        formControlName="country_id" placeholder="Select a country" [appendTo]="'body'"
                        class="w-full" />
                    <app-form-error [control]="vendorForm.controls['country_id']" [controlName]="'Country'"
                        [apiErrorType]="'country_id'" />
                </div>
            </div>
        </form>
    </ng-template>
    <ng-template #footer>
        <div class="flex items-center gap-2">
            <button pButton label="Cancel" icon="pi pi-times" class="w-full" outlined (click)="closeDialog()"></button>
            <button pButton label="Save" icon="pi pi-check" class="w-full" (click)="saveVendor()"></button>
        </div>
    </ng-template>
</p-drawer>
