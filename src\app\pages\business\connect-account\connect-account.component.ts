import { AfterViewInit, Component, inject, Input, OnChanges, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { PrimengModules } from '../../../core/utils/primeng';
import { ApiService } from '../../../core/services/api.service';
import { LoaderService } from '../../../core/services/loader.service';
import { HttpParams } from '@angular/common/http';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { ConfirmationService, MessageService } from 'primeng/api';
import { CreateConnectAccountComponent } from './create-connect-account/create-connect-account.component';
import { ServerApiService } from '../../../core/services/server-api.service';
import { createFormData, dateToString, filterParams } from '../../../core/utils/utils';
import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';
import { FinancialConnectionModal } from './connect-dialog/financialConnection';
import { AddAddressModal } from './connect-dialog/addAddressModal';
import { TableRowCollapseEvent, TableRowExpandEvent } from 'primeng/table';
import { CreditMoneyModal } from './connect-dialog/creditMoneyModal';
import { ConnectAccountIdModal } from './connect-dialog/connectAccountIdModal';
import { ShowBalancePopup } from './connect-dialog/showBalancePopup';
import { AddExternalAccountModal } from './connect-dialog/addExternalAccountModal';
import { Popover } from 'primeng/popover';
import { AddBankAccountModal } from './connect-dialog/addBankAccountModal';
import { FundTransferFromFinancialModal } from './connect-dialog/fundTransferFromFinancial';
import { AuthService } from '../../../core/services/authentication.service';
import { TransferOtherBusinessAccModal } from './connect-dialog/transferOtherBusinessAcc';
import { EditPayoutScheduleModal } from './connect-dialog/editPayoutScheduleModal';
import { PayoutModal } from './connect-dialog/payoutModal';

@Component({
  selector: 'app-connect-account',
  imports: [PrimengModules, CreateConnectAccountComponent],
  standalone: true,
  templateUrl: './connect-account.component.html',
  styleUrl: './connect-account.component.scss',
  providers: [ConfirmationService, DialogService],
})
export class ConnectAccountComponent implements OnInit, AfterViewInit, OnDestroy {
  private apiService = inject(ApiService);
  public loaderService = inject(LoaderService);
  private fb = inject(FormBuilder);
  private messageService = inject(MessageService);
  private serverApiService = inject(ServerApiService);
  private confirmationService = inject(ConfirmationService);
  private dialogService = inject(DialogService);
  public authService = inject(AuthService);

  @ViewChild('DebitOp') DebitOp!: Popover;
  @ViewChild('loadMoneyOp') loadMoneyOp!: Popover;
  @ViewChild('downloadStatementOp') downloadStatementOp!: Popover;
  @ViewChild('payoutOp') payoutOp!: Popover;

  ref: DynamicDialogRef | undefined;

  @Input() business: any = null;

  connectAccounts: any = null;
  connectAccountDialog = false;
  isEdit = false;
  connectAccountForm!: FormGroup;

  financial_accounts: any[] = [];
  bank_accounts: any[] = [];
  countries: any[] = [];

  LoadMoneyAmount = new FormControl(null, [Validators.required, Validators.min(1)]);
  DebitAmount = new FormControl(null, [Validators.required, Validators.min(1)]);
  payoutAmount = new FormControl(null, [Validators.required, Validators.min(1)]);
  statementDateRange = new FormControl(null, [Validators.required]);

  constructor() { }

  ngOnInit() {
    this.fetchCountries();
  }

  ngAfterViewInit() {
    if (this.business) {
      this.fetchConnectAccounts();
    }
  }

  ngOnDestroy() {
    if (this.ref) {
      this.ref.close();
    }
  }

  fetchCountries() {
    this.apiService.get('countries/mini').subscribe({
      next: (res: any) => { this.countries = res; },
      error: (err: any) => { },
      complete: () => { },
    });
  }

  fetchConnectAccounts() {
    this.loaderService.show();
    this.serverApiService.get(`connect/connect-accounts/business/${this.business.id}`).subscribe({
      next: (res: any) => { this.connectAccounts = res; this.fetchFinancialConnections(); this.fetchBankAccounts(); },
      error: (err: any) => { this.loaderService.hide(); },
      complete: () => { this.loaderService.hide(); },
    });
  }

  openDialogToAddAccountId() {
    this.ref = this.dialogService.open(ConnectAccountIdModal, {
      data: { business: this.business },
      header: 'Add Connect Account ID',
      modal: true,
      dismissableMask: false,
      closable: true,
      showHeader: true,
      width: '30vw',
      contentStyle: { overflow: 'auto' },
      breakpoints: {
        '960px': '55vw',
        '640px': '90vw'
      },
    });

    this.ref.onClose.subscribe((data: any) => {
      if (data) {
        this.fetchConnectAccounts();
      }
    });
  }

  handleSave(event: any) {
    this.connectAccountDialog = false;
    this.isEdit = false;
    this.fetchConnectAccounts();
  }

  generateConnectAccount() {
    this.loaderService.show();
    this.serverApiService.post(`connect/connect-accounts/${this.connectAccounts.id}/generate`, null).subscribe({
      next: (res: any) => {
        this.messageService.add({ severity: 'success', summary: 'Successful', detail: 'Connect Account Generated', life: 3000, });
        this.fetchConnectAccounts();
      },
      error: (err: any) => {
        this.loaderService.hide();
        this.messageService.add({ severity: 'error', summary: 'Error', detail: err.error.message, life: 3000, });
      },
      complete: () => { this.loaderService.hide(); },
    });
  }

  loadMoney() {
    if (!this.LoadMoneyAmount.valid) {
      this.LoadMoneyAmount.markAllAsTouched();
      this.LoadMoneyAmount.updateValueAndValidity();
      this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Amount is required', life: 3000, });
      return;
    }
    this.loaderService.show();
    const formData = createFormData({ amount: this.LoadMoneyAmount.value, business_id: this.business.id });
    this.serverApiService.post(`connect/connect-accounts/${this.connectAccounts.id}/load-single`, formData).subscribe({
      next: (res: any) => { this.fetchConnectAccounts(); this.loadMoneyOp.hide(); },
      error: (err: any) => {
        this.loaderService.hide();
        this.messageService.add({ severity: 'error', summary: 'Error', detail: err.error.message, life: 3000, });
      },
      complete: () => { this.loaderService.hide(); },
    });
  }

  debitMoney() {
    if (!this.DebitAmount.valid) {
      this.DebitAmount.markAllAsTouched();
      this.DebitAmount.updateValueAndValidity();
      this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Amount is required', life: 3000, });
      return;
    }
    this.loaderService.show();
    const formData = createFormData({ amount: this.DebitAmount.value, business_id: this.business.id });
    this.serverApiService.post(`connect/connect-accounts/${this.connectAccounts.id}/debit-single`, formData).subscribe({
      next: (res: any) => { this.fetchConnectAccounts(); this.DebitOp.hide(); },
      error: (err: any) => {
        this.loaderService.hide();
        this.messageService.add({ severity: 'error', summary: 'Error', detail: err.error.message, life: 3000, });
      },
      complete: () => { this.loaderService.hide(); },
    });
  }

  /// Handle Payout Schedule Start
  openDialogToEditPayoutSchedule() {
    this.ref = this.dialogService.open(EditPayoutScheduleModal, {
      data: { connectAccounts: this.connectAccounts },
      header: 'Edit Payout Schedule',
      modal: true,
      dismissableMask: false,
      closable: true,
      showHeader: true,
      width: '30vw',
      contentStyle: { overflow: 'auto' },
      breakpoints: {
        '960px': '55vw',
        '640px': '90vw'
      },
    });
  }

  instantPayout() {
    if (!this.payoutAmount.valid) {
      this.payoutAmount.markAllAsTouched();
      this.payoutAmount.updateValueAndValidity();
      this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Amount is required', life: 3000, });
      return;
    }
    this.loaderService.show();
    const formData = createFormData({ amount: this.payoutAmount.value });
    this.serverApiService.post(`connect/connect-accounts/${this.connectAccounts.id}/instant-payout`, formData).subscribe({
      next: (res: any) => {
        this.fetchConnectAccounts(); this.payoutOp.hide();
        this.messageService.add({ severity: 'success', summary: 'Successful', detail: res.message, life: 3000, });
      },
      error: (err: any) => {
        this.loaderService.hide();
        this.messageService.add({ severity: 'error', summary: 'Error', detail: err.error.message, life: 3000, });
      },
      complete: () => { this.loaderService.hide(); },
    });
  }

  showPayoutModal() {
    this.ref = this.dialogService.open(PayoutModal, {
      data: { connectAccounts: this.connectAccounts },
      header: 'PayouPayout to businesst',
      modal: true,
      dismissableMask: false,
      closable: true,
      showHeader: true,
      width: '30vw',
      contentStyle: { overflow: 'auto' },
      breakpoints: {
        '960px': '55vw',
        '640px': '90vw'
      },
    });
  }
  /// Handle Payout Schedule End

  //// Handle External Account  Start

  openDialogToAddExternalAccount(isEdit: boolean, externalAccount?: any) {
    this.ref = this.dialogService.open(AddExternalAccountModal, {
      data: {
        business: this.business,
        connectAccounts_id: this.connectAccounts.id,
        isEdit: isEdit,
        countries: this.countries,
        externalAccount: externalAccount,
      },
      header: `${isEdit ? 'Edit' : 'Add'} External Account`,
      modal: true,
      dismissableMask: false,
      closable: true,
      showHeader: true,
      width: '30vw',
      contentStyle: { overflow: 'auto' },
      breakpoints: {
        '960px': '55vw',
        '640px': '90vw'
      },
    });
    this.ref.onClose.subscribe((data: any) => {
      if (data && !isEdit) {
        this.connectAccounts.external_accounts.splice(0, 0, data);
      } else if (data && isEdit) {
        let index = this.connectAccounts.external_accounts.findIndex((a: any) => a.id === externalAccount.id);
        this.connectAccounts.external_accounts[index] = data;
      }
    });
  }

  toggleStatusExternalAccount(event: any, externalAccount: any) {
    externalAccount.is_customer = event.checked ? true : false;
    this.loaderService.show();
    const url = externalAccount.is_customer ? `connect/connect-accounts/${externalAccount.id}/enable-external-customer` : `connect/connect-accounts/${externalAccount.id}/disable-external-customer`;
    this.serverApiService.post(url, null).subscribe({
      next: (res: any) => {
        this.connectAccounts.external_accounts.find((a: any) => a.id === externalAccount.id).is_customer = externalAccount.is_customer;
      },
      error: (err: any) => { this.loaderService.hide(); },
      complete: () => { this.loaderService.hide(); },
    });
  }

  makeDefaultExternalAccount(externalAccount: any) {
    externalAccount.default = true;
    const formData = createFormData(externalAccount);
    this.loaderService.show();
    this.serverApiService.post(`connect/connect-accounts/${externalAccount.connect_account_id}/external-accounts/${externalAccount.id}`, formData).subscribe({
      next: (res: any) => {
        this.connectAccounts.external_accounts.forEach((a: any) => {
          if (a.id !== externalAccount.id) {
            a.default = false;
          } else {
            a.default = true;
          }
        });
      },
      error: (err: any) => { this.loaderService.hide(); },
      complete: () => { this.loaderService.hide(); },
    });
  }

  deleteExternalAccount(externalAccount: any) {
    this.confirmationService.confirm({
      message: 'Are you sure you want to delete ' + externalAccount.account_holder_name + '?',
      header: 'Confirm',
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        this.loaderService.show();
        this.serverApiService.delete(`connect/connect-accounts/${externalAccount.id}/external-accounts`).subscribe({
          next: (res: any) => {
            this.connectAccounts.external_accounts = this.connectAccounts.external_accounts.filter((a: any) => a.id !== externalAccount.id);
          },
          error: (err: any) => { this.loaderService.hide(); },
          complete: () => { this.loaderService.hide(); },
        });

      },
    });
  }

  //// Handle External Account  End

  //// Handle Financial Account Start

  fetchFinancialConnections() {
    const httpParams: HttpParams = filterParams({ nopaginate: 1 });
    this.loaderService.show();
    this.serverApiService.get(`connect/connect-accounts/${this.connectAccounts.id}/financial-accounts`, httpParams).subscribe({
      next: (res: any) => { this.financial_accounts = res; },
      error: (err: any) => { this.loaderService.hide(); this.financial_accounts = []; },
      complete: () => { this.loaderService.hide(); },
    });
  }

  fetchFinancialConnectionsHardRefresh() {
    const httpParams: HttpParams = filterParams({ nopaginate: 1 });
    this.loaderService.show();
    this.serverApiService.get(`connect/connect-accounts/${this.connectAccounts.id}/financial-accounts/addresses/hard-refresh`, httpParams).subscribe({
      next: (res: any) => { this.financial_accounts = res; },
      error: (err: any) => { this.loaderService.hide(); },
      complete: () => { this.loaderService.hide(); },
    });
  }

  openDialogToAddFinancialConnection() {
    this.ref = this.dialogService.open(FinancialConnectionModal, {
      data: { connectAccounts_id: this.connectAccounts.id },
      header: 'Add Financial Connection',
      modal: true,
      dismissableMask: false,
      closable: true,
      showHeader: true,
      width: '30vw',
      contentStyle: { overflow: 'auto' },
      breakpoints: {
        '960px': '55vw',
        '640px': '90vw'
      },
    });

    this.ref.onClose.subscribe((data: any) => {
      if (data) {
        this.fetchFinancialConnections();
      }
    });
  }

  viewFinancialAccountBalance(financialAccount: any) {
    this.ref = this.dialogService.open(ShowBalancePopup, {
      data: {
        connectAccounts_id: this.connectAccounts.id,
        financialAccount_id: financialAccount.id,
      },
      header: `View Balance (${financialAccount?.metadata.nickname})`,
      modal: true,
      dismissableMask: false,
      closable: true,
      showHeader: true,
      width: '40vw',
      contentStyle: { overflow: 'auto' },
      styleClass: 'bg-gray-100',
      breakpoints: {
        '960px': '55vw',
        '640px': '90vw'
      },
    });
  }

  openDialogToAddAddress(financialAccount: any) {
    this.loaderService.show();
    this.serverApiService.post(`connect/connect-accounts/${this.connectAccounts.id}/financial-accounts/${financialAccount.id}/addresses`, { currency: financialAccount.holds_currencies }).subscribe({
      next: (res: any) => { this.fetchFinancialConnections(); },
      error: (err: any) => {
        this.loaderService.hide();
        this.messageService.add({ severity: 'error', summary: 'Error', detail: err.error.message, life: 3000, });
      },
      complete: () => { this.loaderService.hide(); },
    });
  }

  OpenCreditMoneyModal(financialAccount: any) {
    this.ref = this.dialogService.open(CreditMoneyModal, {
      data: {
        connectAccounts_id: this.connectAccounts.id,
        financialAccount_id: financialAccount.id,
        financialAddress_id: financialAccount.financial_addresses.id,
      },
      header: `Credit Money (${financialAccount?.metadata.nickname})`,
      modal: true,
      dismissableMask: false,
      closable: true,
      showHeader: true,
      width: '30vw',
      contentStyle: { overflow: 'auto' },
      breakpoints: {
        '960px': '55vw',
        '640px': '90vw'
      },
    });

    this.ref.onClose.subscribe((data: any) => {
      if (data) {
        this.fetchFinancialConnections();
      }
    });
  }

  fundTransferFromFinancialAccount(financialAccount: any) {
    this.ref = this.dialogService.open(FundTransferFromFinancialModal, {
      data: {
        connectAccounts_id: this.connectAccounts.id,
        financialAccount_id: financialAccount.id,
        bank_accounts: this.bank_accounts.filter((a: any) => !a.archived),
      },
      header: `Fund Transfer (${financialAccount?.metadata.nickname})`,
      modal: true,
      dismissableMask: false,
      closable: true,
      showHeader: true,
      width: '30vw',
      contentStyle: { overflow: 'auto' },
      breakpoints: {
        '960px': '55vw',
        '640px': '90vw'
      },
    });
    this.ref.onClose.subscribe((data: any) => {
      if (data) {
        // this.fetchFinancialConnections();
      }
    });
  }

  deleteFinancialConnection(financialAccount: any) {
    this.confirmationService.confirm({
      message: 'Are you sure you want to delete ' + financialAccount.financial_addresses.account_holder_name + '?',
      header: 'Confirm',
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        this.loaderService.show();
        this.serverApiService.delete(`connect/connect-accounts/${this.connectAccounts.id}/financial-accounts/${financialAccount.id}`).subscribe({
          next: (res: any) => { this.fetchFinancialConnections(); },
          error: (err: any) => {
            this.loaderService.hide();
            this.messageService.add({ severity: 'error', summary: 'Error', detail: err.error.message, life: 3000, });
          },
          complete: () => { this.loaderService.hide(); },
        });

      },
    });
  }

  downloadStatement(financialAccount: any) {
    if (!this.statementDateRange.value) {
      this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Date range is required', life: 3000, });
      return;
    }
    this.downloadStatementOp.hide();
    const url = `${this.serverApiService.baseUrl}/connect/connect-accounts/${this.connectAccounts.id}/financial-accounts/${financialAccount.id}/statement?from_date=${dateToString(this.statementDateRange.value[0])}&to_date=${dateToString(this.statementDateRange.value[1])}`;
    window.open(url, '_blank');
  }

  transferBetweenOtherBusinessAccounts(financialAccount: any) {
    this.ref = this.dialogService.open(TransferOtherBusinessAccModal, {
      data: {
        connectAccounts_id: this.connectAccounts.id,
        financialAccount_id: financialAccount.id,
        business: this.business,
      },
      header: `Transfer Between Other Business Accounts (${financialAccount?.metadata.nickname})`,
      modal: true,
      dismissableMask: false,
      closable: true,
      showHeader: true,
      width: '30vw',
      contentStyle: { overflow: 'auto' },
      breakpoints: {
        '960px': '55vw',
        '640px': '90vw'
      },
    });
  }
  //// Handle Financial Account End

  //// Handle Bank Account Start

  fetchBankAccounts() {
    const httpParams: HttpParams = filterParams({ nopaginate: 1 });
    this.loaderService.show();
    this.serverApiService.get(`connect/connect-accounts/${this.connectAccounts.id}/bank-accounts`, httpParams).subscribe({
      next: (res: any) => { this.bank_accounts = res; },
      error: (err: any) => { this.loaderService.hide(); this.bank_accounts = []; },
      complete: () => { this.loaderService.hide(); },
    });
  }

  fetchBankAccountsHardRefresh() {
    const httpParams: HttpParams = filterParams({ nopaginate: 1 });
    this.loaderService.show();
    this.serverApiService.get(`connect/connect-accounts/${this.connectAccounts.id}/bank-accounts/hard-refresh`, httpParams).subscribe({
      next: (res: any) => { this.bank_accounts = res; },
      error: (err: any) => { this.loaderService.hide(); },
      complete: () => { this.loaderService.hide(); },
    });
  }

  openDialogToAddBankAccount(bankAccount?: any) {
    this.ref = this.dialogService.open(AddBankAccountModal, {
      data: {
        connectAccounts_id: this.connectAccounts.id,
        bankAccount: bankAccount,
      },
      header: `${bankAccount ? 'Edit' : 'Add'} Bank Account`,
      modal: true,
      dismissableMask: false,
      closable: true,
      showHeader: true,
      width: '30vw',
      contentStyle: { overflow: 'auto' },
      breakpoints: {
        '960px': '55vw',
        '640px': '90vw'
      },
    });
    this.ref.onClose.subscribe((data: any) => {
      if (data) {
        this.fetchBankAccounts();
      }
    });
  }

  deleteBankAccount(bankAccount: any) {
    this.confirmationService.confirm({
      message: 'Are you sure you want to delete ' + bankAccount.bank_name + '?',
      header: 'Confirm',
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        this.loaderService.show();
        this.serverApiService.delete(`connect/connect-accounts/${this.connectAccounts.id}/bank-accounts/${bankAccount.id}`).subscribe({
          next: (res: any) => { this.fetchBankAccounts(); },
          error: (err: any) => { this.loaderService.hide(); },
          complete: () => { this.loaderService.hide(); },
        });

      },
    });
  }

  //// Handle Bank Account End

  getSeverity(status: string) {
    switch (status) {
      case 'active': return 'success';
      case 'archived': return 'info';
      case 'pending': return 'info';
      case 'failed': return 'danger';
      default: return 'info';
    }
  }
}
