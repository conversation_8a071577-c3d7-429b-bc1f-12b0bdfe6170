import { Component, inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MenuItem } from 'primeng/api';
import { AppMenuitem } from './app.menuitem';
import { AuthService } from '../../core/services/authentication.service';

@Component({
  selector: 'app-menu',
  standalone: true,
  imports: [CommonModule, AppMenuitem, RouterModule],
  template: `<ul class="layout-menu">
              <ng-container *ngFor="let item of model; let i = index">
                  <li app-menuitem [item]="item" [index]="i" [root]="true" class="layout-root-menuitem"></li>
                  <li *ngIf="item.separator" class="menu-separator"></li>
              </ng-container>
            </ul> `
})
export class AppMenu implements OnInit {
  private authService = inject(AuthService);
  model: MenuItem[] = [];

  constructor() {
    this.getMenu();
  }

  ngOnInit() { }

  getMenu() {
    this.model = [
      {
        label: 'Home',
        items: [{ label: 'Dashboard', icon: 'pi pi-fw pi-home', routerLink: ['/home'] }],
        visible: (this.authService._superman || this.authService._permissions?.dashboard?.actions?.list) || false,
        separator: true
      },
      {
        label: 'Professionals',
        visible: (this.authService._superman || this.authService._permissions?.businesses?.actions?.list || this.authService._permissions?.vendors?.actions?.list) || false,
        items: [
          {
            label: 'Business',
            icon: 'pi pi-fw pi-briefcase',
            routerLink: ['/business'],
            visible: (this.authService._superman || this.authService._permissions?.businesses?.actions?.list) || false
          },
          {
            label: 'Vendors',
            icon: 'pi pi-fw pi-users',
            routerLink: ['/vendors'],
            visible: (this.authService._superman || this.authService._permissions?.vendors?.actions?.list) || false
          }
        ],
        separator: true
      },
      {
        label: 'Franchise',
        visible: (this.authService._superman || this.authService._permissions?.owners?.actions?.list) || false,
        items: [
          {
            label: 'Owner',
            icon: 'pi pi-fw pi-building',
            routerLink: ['/franchise/owner'],
            visible: (this.authService._superman || this.authService._permissions?.owners?.actions?.list) || false
          },
        ],
        separator: true
      },
      {
        label: 'Account Management',
        visible: (this.authService._superman) || false,
        items: [
          {
            label: 'Account Owner',
            icon: 'pi pi-fw pi-building-columns',
            routerLink: ['/account-owner'],
            visible: (this.authService._superman) || false
          },
        ],
        separator: true
      },
      {
        label: 'Transactions Management',
        visible: (this.authService._superman || this.authService._permissions?.transactions?.actions?.list || this.authService._permissions?.receive_credit?.actions?.list) || false,
        items: [
          {
            label: 'Transactions',
            icon: 'pi pi-fw pi-money-bill',
            routerLink: ['/transactions'],
            visible: (this.authService._superman || this.authService._permissions?.transactions?.actions?.list) || false
          },
          {
            label: 'Receive Credits',
            icon: 'pi pi-fw pi-credit-card',
            routerLink: ['/receive-credit'],
            visible: (this.authService._superman || this.authService._permissions?.receive_credit?.actions?.list) || false
          }
        ],
        separator: true
      },
      {
        label: 'Admins Management',
        // visible: (this.authService._superman || this.authService._permissions?.admins?.actions?.list || this.authService._permissions?.roles?.actions?.list) || false,
        items: [
          {
            label: 'Admins',
            icon: 'pi pi-fw pi-user',
            items: [
              {
                label: 'All Admins',
                icon: 'pi pi-fw pi-sign-in',
                routerLink: ['/admins'],
                visible: (this.authService._superman || this.authService._permissions?.admins?.actions?.list) || false
              },
              {
                label: 'Roles',
                icon: 'pi pi-fw pi-lock',
                routerLink: ['/admin/role'],
                // visible: (this.authService._superman || this.authService._permissions?.roles?.actions?.list) || false
              },
            ]
          },
        ],
        separator: true
      },
      {
        label: 'Settings',
        visible: (this.authService._superman || this.authService._permissions?.country?.actions?.list) || false,
        items: [
          {
            label: 'Country',
            icon: 'pi pi-fw pi-globe',
            routerLink: ['/country'],
            visible: (this.authService._superman || this.authService._permissions?.country?.actions?.list) || false
          },
          {
            label: 'Site Settings',
            icon: 'pi pi-fw pi-cog',
            routerLink: ['/site-settings'],
            visible: (this.authService._superman || this.authService._permissions?.site_settings?.actions?.list) || false
          }
        ],
        separator: true
      },
      {
        label: 'Documents',
        visible: (this.authService._superman || this.authService._permissions?.country?.actions?.list) || false,
        items: [
          {
            label: 'Api Documents',
            icon: 'pi pi-fw pi-box',
            routerLink: ['/api-document'],
            visible: (this.authService._superman) || false
          },
        ]
      },
    ];
  }
}
