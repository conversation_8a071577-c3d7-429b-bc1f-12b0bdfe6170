<p-table #dt [value]="transactions" [rows]="10" [paginator]="true" [resetPageOnSort]="false" [lazy]="true"
    (onLazyLoad)="onLazyLoad($event)" [rowHover]="true" dataKey="id" [totalRecords]="totalRecords"
    [tableStyle]="{ 'min-width': '100rem' }" [scrollable]="true" scrollHeight="80vh"
    currentPageReportTemplate="Showing {first} to {last} of {totalRecords} transactions" [showCurrentPageReport]="true"
    [rowsPerPageOptions]="[10, 20, 30]">
    <ng-template #caption>
        <p-toolbar [style]="{'border': 'none','padding': '0'}">
            <ng-template #start>
                <p class="font-medium font-bold text-2xl">Transactions</p>
            </ng-template>

            <ng-template #end>
                <p-iconfield class="mr-2">
                    <p-inputicon styleClass="pi pi-search" />
                    <input pInputText type="text" [formControl]="search" placeholder="Search..." />
                </p-iconfield>
                <p-datepicker [formControl]="date_range" selectionMode="range" [readonlyInput]="true" class="mr-2"
                    [showClear]="true" placeholder="Select a date range" pTooltip="Filter by date range"
                    tooltipPosition="top" dateFormat="dd-mm-yy" [appendTo]="'body'" />
                <p-button label="Hard Refresh" icon="pi pi-refresh" severity="help" class="mr-2"
                    (click)="fetchTransactionsHardRefresh()" />
                @if (transactions.length > 0) {
                    <p-button label="Excel" icon="pi pi-download" class="mr-2" (click)="exportToExcel()" />
                }
            </ng-template>
        </p-toolbar>

    </ng-template>
    <ng-template #header>
        <tr>
            <th pSortableColumn="s_bank_transaction_id">
                Transaction ID
                <p-sortIcon field="s_bank_transaction_id" />
            </th>
            <!-- category -->
            <th pSortableColumn="category">
                Category
                <p-sortIcon field="category" />
            </th>
            <!-- amount -->
            <th pSortableColumn="amount">
                Amount
                <p-sortIcon field="amount" />
            </th>
            <!-- currency -->
            <th pSortableColumn="currency">
                Currency
                <p-sortIcon field="currency" />
            </th>
            <!-- created -->
            <th pSortableColumn="created">
                Created
                <p-sortIcon field="created" />
            </th>
            <!-- status -->
            <th>
                Status
            </th>
            <th>Action</th>

        </tr>
    </ng-template>
    <ng-template #body let-transaction>
        <tr>
            <td>{{ transaction?.s_bank_transaction_id }}</td>
            <td [ngClass]="getCategoryColor(transaction?.category)">
                {{ transaction?.category.split('_').join(' ') | titlecase }}
            </td>
            <td [ngClass]="{'text-green-500': transaction?.amount > 0, 'text-red-500': transaction?.amount < 0}">{{
                (transaction?.amount/100) | currency:'GBP':'symbol':'1.2-2' }}</td>
            <td>{{ transaction?.currency | uppercase }}</td>
            <td>{{ transaction?.created | date:'dd-MM-yyyy HH:mm' }}</td>
            <td>
                <p-tag [severity]="getSeverity(transaction?.status)" value="{{ transaction?.status | titlecase }}" />
            </td>
            <td>
                <!-- <p-button icon="pi pi-eye" class="mr-2" [rounded]="true" [outlined]="true" (click)="viewTransaction(transaction)" /> -->
                <p-button pTooltip="Download Slip" class="mr-2" [rounded]="true" [outlined]="true" tooltipPosition="top"
                    icon="pi pi-download" severity="info" class="mr-2" (click)="downloadSlip(transaction)" />
            </td>
        </tr>

    </ng-template>
    <ng-template #emptymessage>
        <tr>
            <td colspan="9" style="text-align: center !important;">No records found</td>
        </tr>
    </ng-template>
</p-table>

<p-confirmdialog [style]="{ width: '450px' }" />


<p-dialog header="View Transaction" [(visible)]="id_view_transaction" [modal]="true" [dismissableMask]="true"
    [style]="{ width: '50vw' }">
    <p>Transaction ID: {{ transaction?.s_bank_transaction_id }}</p>
    <p>Category: {{ transaction?.category }}</p>
    <p>Amount: {{ transaction?.amount }}</p>
    <p>Currency: {{ transaction?.currency }}</p>
    <p>Created: {{ transaction?.created }}</p>
    <p>Status: {{ transaction?.status }}</p>
</p-dialog>
