import { Component, OnInit, inject } from '@angular/core';
import { DialogService, DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { PrimengModules } from '../../../../core/utils/primeng';
import { MessageService } from 'primeng/api';
import { LoaderService } from '../../../../core/services/loader.service';
import { ServerApiService } from '../../../../core/services/server-api.service';

@Component({
  selector: 'app-credit-money-modal',
  imports: [PrimengModules],
  template: `
    <div class="w-full">
      <div class="p-4 space-y-4">

    <!-- Available Balance -->
    <div class="flex items-center justify-between bg-green-50 border border-green-300 rounded-xl p-4 shadow-sm">
      <div>
        <h3 class="text-lg font-semibold text-green-700">Available Balance</h3>
        <p class="text-2xl font-bold text-green-800">
          {{ (account?.available?.gbp?.value/100)  | currency:'GBP':'symbol':'1.2-2' }}
        </p>
      </div>
      <i class="pi pi-wallet text-green-600 text-4xl"></i>
    </div>

    <!-- Pending Balances -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">

      <!-- Inbound Pending -->
      <div class="flex items-center justify-between bg-blue-50 border border-blue-300 rounded-xl p-4 shadow-sm">
        <div>
          <h4 class="text-md font-semibold text-blue-700">Inbound Pending</h4>
          <p class="text-xl font-bold text-blue-800">
            {{ (account?.inbound_pending?.gbp?.value/100) | currency:'GBP':'symbol':'1.2-2' }}
          </p>
        </div>
        <i class="pi pi-arrow-down text-blue-600 text-3xl"></i>
      </div>

      <!-- Outbound Pending -->
      <div class="flex items-center justify-between bg-orange-50 border border-orange-300 rounded-xl p-4 shadow-sm">
        <div>
          <h4 class="text-md font-semibold text-orange-700">Outbound Pending</h4>
          <p class="text-xl font-bold text-orange-800">
            {{ (account?.outbound_pending?.gbp?.value/100) | currency:'GBP':'symbol':'1.2-2' }}
          </p>
        </div>
        <i class="pi pi-arrow-up text-orange-600 text-3xl"></i>
      </div>

    </div>
  </div>
    </div>
<div class="flex justify-end gap-2 mt-6 border-t border-gray-200 pt-2">
    <p-button label="Close" icon="pi pi-times"  (click)="close()" />
</div>
            `,
  providers: [DialogService],
})
export class ShowBalancePopup implements OnInit {
  private messageService = inject(MessageService);
  private loaderService = inject(LoaderService);
  private serverApiService = inject(ServerApiService);
  private ref = inject(DynamicDialogRef);
  private config = inject(DynamicDialogConfig);

  account: any = null;
  connectAccounts_id: any = null;
  financialAccount_id: any = null;
  ngOnInit() {
    this.connectAccounts_id = this.config.data.connectAccounts_id;
    this.financialAccount_id = this.config.data.financialAccount_id;
    this.viewBalance();
  }


  viewBalance() {
    this.loaderService.show();
    this.serverApiService.get(`connect/connect-accounts/${this.connectAccounts_id}/financial-accounts/${this.financialAccount_id}`).subscribe({
      next: (res: any) => {
        this.account = res.stripe_financial_account.balance;
      },
      error: (err: any) => {
        this.account = null;
        this.loaderService.hide();
        this.messageService.add({ severity: 'error', summary: 'Error', detail: err.error.message, life: 3000, });
      },
      complete: () => { this.loaderService.hide(); },
    });
  }

  close() {
    this.ref.close();
  }
}
