import { Component, inject, OnInit, ViewChild } from '@angular/core';
import { Table, TableLazyLoadEvent, TablePageEvent } from 'primeng/table';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { ConfirmationService, MessageService } from 'primeng/api';
import { ActivatedRoute, Router } from '@angular/router';
import { debounceTime } from 'rxjs';
import { HttpParams } from '@angular/common/http';
import { PrimengModules } from '../../../core/utils/primeng';
import { ApiService } from '../../../core/services/api.service';
import { LoaderService } from '../../../core/services/loader.service';
import { dateToString, filterParams } from '../../../core/utils/utils';
import { ServerApiService } from '../../../core/services/server-api.service';

@Component({
  selector: 'app-receive-credits',
  imports: [PrimengModules],
  standalone: true,
  templateUrl: './receive-credits.component.html',
  styleUrl: './receive-credits.component.scss',
  providers: [ConfirmationService],
})
export class ReceiveCreditsComponent {
  private confirmationService = inject(ConfirmationService);
  private messageService = inject(MessageService);
  private fb = inject(FormBuilder);
  public loaderService = inject(LoaderService);
  private serverApiService = inject(ServerApiService);

  @ViewChild('dt') dt!: Table;

  filterForm!: FormGroup;
  search = new FormControl('');
  date_range = new FormControl();
  receiveCredits: any[] = [];
  totalRecords: number = 0;


  constructor() {
    this.filterForm = this.fb.group({
      page: [],
      per_page: [],
      pagination: [true],
      from_date: [],
      to_date: [],
    });
  }

  ngOnInit() {
    this.search.valueChanges.pipe(debounceTime(400)).subscribe((val) => {
      this.fetchReceiveCredits();
    });
    this.date_range.valueChanges.pipe(debounceTime(400)).subscribe((val: any) => {
      if (!val) {
        this.filterForm.controls['from_date'].setValue(null);
        this.filterForm.controls['to_date'].setValue(null);
        this.filterForm.controls['page'].setValue(1, { emitEvent: false });
        this.fetchReceiveCredits();
        return;
      }
      if (val[0] && val[1]) {
        this.filterForm.controls['from_date'].setValue(dateToString(val[0]));
        this.filterForm.controls['to_date'].setValue(dateToString(val[1]));
        this.filterForm.controls['page'].setValue(1, { emitEvent: false });
        this.fetchReceiveCredits();
      }
    });
  }

  fetchReceiveCredits() {
    const httpParams: HttpParams = filterParams({ ...this.filterForm.value, query: this.search.value, });
    this.loaderService.show();
    this.serverApiService.get('connect/receive-credits', httpParams).subscribe({
      next: (res: any) => { this.receiveCredits = res.data; this.totalRecords = res.total; },
      error: (err: any) => { this.loaderService.hide(); this.receiveCredits = []; this.totalRecords = 0; },
      complete: () => { this.loaderService.hide(); },
    });
  }

  fetchReceiveCreditsHardRefresh() {
    const httpParams: HttpParams = filterParams({ nopaginate: 1 });
    this.loaderService.show();
    this.serverApiService.get('connect/receive-credits/hard-refresh', httpParams).subscribe({
      next: (res: any) => { this.fetchReceiveCredits(); },
      error: (err: any) => { this.loaderService.hide(); },
      complete: () => { this.loaderService.hide(); },
    });
  }

  onLazyLoad(event: any) {
    if (
      event.first >= 0 &&
      event.rows &&
      (this.filterForm.value.page !== event.first / event.rows + 1 ||
        this.filterForm.value.per_page !== event.rows)
    ) {
      this.filterForm.controls['page'].setValue(event.first / event.rows + 1, { emitEvent: false });
      this.filterForm.controls['per_page'].setValue(event.rows, { emitEvent: false });
      this.fetchReceiveCredits();
    }
    if (event.sortField && event.sortOrder) {
      this.receiveCredits.sort((a, b) => {
        if (a[event.sortField] < b[event.sortField]) {
          return event.sortOrder === 1 ? -1 : 1;
        }
        if (a[event.sortField] > b[event.sortField]) {
          return event.sortOrder === 1 ? 1 : -1;
        }
        return 0;
      });
    }
  }

  getSeverity(status: string) {
    switch (status) {
      case 'succeeded': return 'success';
      case 'failed': return 'danger';
      default: return 'info';
    }
  }

  exportToExcel() {
    const url = `${this.serverApiService.baseUrl}/connect/receive-credits/export?from_date=${this.filterForm.value.from_date}&to_date=${this.filterForm.value.to_date}&format=excel`;
    window.open(url, '_blank');
  }
}
