<div class="flex h-screen font-sans antialiased">
  <!-- Left Sidebar for Navigation -->
  <nav class="w-64 flex-none border-r overflow-y-auto hidden md:block">
    <div class="sticky top-0 p-6 space-y-8">
      <div class="flex items-center space-x-2">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
            d="M10 20l4-16m4 4l4 4m-4 4l-4 4M4 4l4 4M4 12h16" />
        </svg>
        <span class="font-bold text-lg">My API Docs</span>
      </div>

      <div class="space-y-4 text-sm">
        <!-- Getting Started Section -->
        <div class="space-y-2">
          <h6 class="font-semibold uppercase tracking-wide">Getting Started</h6>
          <ul class="space-y-1">
            <li><a href="#" class="block hover:text-indigo-600 p-2 rounded-md transition-colors">Introduction</a>
            </li>
            <li><a href="#" class="block hover:text-indigo-600 p-2 rounded-md transition-colors">Authentication</a>
            </li>
            <li><a href="#" class="block hover:text-indigo-600 p-2 rounded-md transition-colors">Errors</a>
            </li>
          </ul>
        </div>
        <!-- Core Resources Section -->
        <div class="space-y-2">
          <h6 class="font-semibold uppercase tracking-wide">Core Resources</h6>
          <ul class="space-y-1">
            <li><a href="#" class="block hover:text-indigo-600 p-2 rounded-md transition-colors">Users</a>
            </li>
            <li><a href="#" class="block hover:text-indigo-600 p-2 rounded-md transition-colors">Products</a>
            </li>
            <li><a href="#" class="block hover:text-indigo-600 p-2 rounded-md transition-colors">Payments</a>
            </li>
            <li><a href="#" class="block hover:text-indigo-600 p-2 rounded-md transition-colors">Invoices</a>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </nav>

  <!-- Main Content and Code Example -->
  <main class="flex-grow overflow-y-auto flex flex-col">
    <!-- Documentation Content -->
    <article class="flex-grow p-8  mx-auto ">
      <header class="mb-8">
        <span class="text-sm font-semibold text-indigo-600 bg-indigo-50 px-3 py-1 rounded-full">CORE
          RESOURCE</span>
        <h1 class="text-4xl font-bold mt-4">The Payments API</h1>
        <p class="mt-2">Create and manage one-time or recurring payments for your users.</p>
      </header>

      <section class="space-y-8">
        <!-- Overview -->
        <div>
          <h2 class="text-2xl font-bold">Overview</h2>
          <p class="mt-4 leading-relaxed">
            The Payments API allows you to programmatically create and process payments securely. Our API is
            built on REST principles, with predictable, resource-oriented URLs. It uses standard HTTP
            response codes and authentication, and accepts and returns JSON.
          </p>
        </div>

        <!-- Endpoint: Create a Payment -->
        <div>
          <h2 class="text-2xl font-bold">Create a Payment</h2>
          <div class="mt-4 p-4 bg-green-50 text-green-800 rounded-lg font-mono text-sm">
            <span class="font-bold">POST</span> /api/v1/payments
          </div>
          <p class="mt-4 leading-relaxed">
            Creates a new payment. You must provide a valid user ID and amount. The currency is assumed to
            be USD unless specified otherwise.
          </p>

          <h3 class="text-xl font-bold mt-6 mb-2">Parameters</h3>
          <div class="overflow-x-auto">
            <table class="w-full text-sm text-left">
              <thead class="text-xs  uppercase">
                <tr>
                  <th scope="col" class="px-6 py-3">Parameter</th>
                  <th scope="col" class="px-6 py-3">Type</th>
                  <th scope="col" class="px-6 py-3">Required</th>
                  <th scope="col" class="px-6 py-3">Description</th>
                </tr>
              </thead>
              <tbody>
                <tr class=" border-b">
                  <td class="px-6 py-4 font-medium whitespace-nowrap">userId</td>
                  <td class="px-6 py-4 font-mono">string</td>
                  <td class="px-6 py-4 text-green-600">Yes</td>
                  <td class="px-6 py-4">The ID of the user to charge.</td>
                </tr>
                <tr class=" border-b">
                  <td class="px-6 py-4 font-medium whitespace-nowrap">amount</td>
                  <td class="px-6 py-4 font-mono">number</td>
                  <td class="px-6 py-4 text-green-600">Yes</td>
                  <td class="px-6 py-4">The amount to charge, in cents.</td>
                </tr>
                <tr class="">
                  <td class="px-6 py-4 font-medium whitespace-nowrap">currency</td>
                  <td class="px-6 py-4 font-mono">string</td>
                  <td class="px-6 py-4 text-red-600">No</td>
                  <td class="px-6 py-4">The currency code (e.g., 'usd').</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </section>
    </article>

    <!-- Right Code Example Panel -->
    <aside class="flex-none p-8 overflow-y-auto font-mono text-sm">
      <div class="sticky top-0 space-y-8">
        <div class="space-y-4">
          <h3 class="text-xl font-bold">Request Example (Node.js)</h3>
          <div class="rounded-md p-4">
            <pre><code class="language-javascript">
const axios = require('axios');
const API_KEY = 'sk_test_...';

async function createPayment() &#123;
  try &#123;
    const response = await axios.post(
      'https://api.myapi.com/v1/payments',
      &#123;
        userId: 'user_1a2b3c',
        amount: 5000,
        currency: 'usd'
      &#125;,
      &#123;
        headers: &#123;
          'Authorization': &#96;Bearer &#36;&#123;API_KEY&#125;&#96;,
          'Content-Type': 'application/json'
        &#125;
      &#125;
    );
    console.log(response.data);
  &#125; catch (error) &#123;
    console.error(error.response.data);
  &#125;
&#125;

createPayment();
                </code></pre>
          </div>
        </div>

        <div class="space-y-4">
          <h3 class="text-xl font-bold">Response Example (JSON)</h3>
          <div class="rounded-md p-4">
            <pre><code class="language-json">
&#123;
  "id": "pay_xyz123",
  "object": "payment",
  "amount": 5000,
  "currency": "usd",
  "status": "succeeded",
  "created": 1672531200,
  "metadata": &#123;&#125;
&#125;
                </code></pre>
          </div>
        </div>
      </div>
    </aside>
  </main>


</div>