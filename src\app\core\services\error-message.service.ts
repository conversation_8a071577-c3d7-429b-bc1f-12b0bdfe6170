import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export interface ErrorMessage {
  id: number;
  error: string;
  type: string;
  serviceUrl: string;
}

@Injectable({
  providedIn: 'root'
})
export class ErrorMessageService {
  private _errors: ErrorMessage[] = [];
  private errorsSubject = new BehaviorSubject<ErrorMessage[]>([]);
  public errors$: Observable<ErrorMessage[]> = this.errorsSubject.asObservable();

  constructor() {}

  get errors(): ErrorMessage[] {
    return this._errors;
  }

  public set(error: string, type: string, serviceUrl: string): void {
    const newError: ErrorMessage = {
      id: Date.now(),
      error,
      type,
      serviceUrl
    };
    this._errors.push(newError);
    this.errorsSubject.next(this._errors);
  }

  public clear(): void {
    this._errors = [];
    this.errorsSubject.next(this._errors);
  }
}
